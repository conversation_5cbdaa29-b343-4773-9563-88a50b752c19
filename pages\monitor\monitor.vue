<template>
  <view class="monitor">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">AI学习监测</text>
      <text class="help-button" @click="showHelp">?</text>
    </view>

    <!-- 摄像头预览区域 -->
    <view class="camera-container" v-if="isMonitoring">
      <view class="camera-header">
        <text class="camera-title">学习状态监测</text>
        <text class="camera-status" :class="{ 'analyzing': isAnalyzing }">
          {{ isAnalyzing ? '分析中...' : (useAI ? 'AI识别模式' : '随机模式') }}
        </text>
      </view>

      <!-- H5兼容的摄像头预览 -->
      <view class="camera-preview-container">
        <video
          v-if="cameraSupported && cameraStream"
          ref="videoElement"
          class="camera-preview"
          autoplay
          muted
          playsinline>
        </video>
        <view v-else-if="!cameraSupported" class="camera-fallback">
          <text class="fallback-text">当前浏览器不支持摄像头功能</text>
          <text class="fallback-hint">请使用支持摄像头的浏览器或移动端应用</text>
        </view>
        <view v-else class="camera-loading">
          <text class="loading-text">正在启动摄像头...</text>
        </view>
        <canvas ref="canvasElement" class="capture-canvas" style="display: none;"></canvas>
      </view>

      <view class="camera-controls">
        <view class="control-group">
          <view class="ai-toggle">
            <text class="toggle-label">AI识别:</text>
            <switch :checked="useAI" @change="onAIToggleChange" />
          </view>
          <view class="auto-toggle">
            <text class="toggle-label">自动拍照:</text>
            <switch :checked="autoCapture" @change="onAutoCaptureChange" />
          </view>
        </view>
        <view class="status-info">
          <text class="status-text">
            {{ isAnalyzing ? '正在分析...' :
               (autoCapture && cameraStream ? '每20秒自动拍照' :
                (!cameraStream ? '摄像头未就绪' : '手动模式')) }}
          </text>
        </view>
      </view>
      <view class="confidence-info" v-if="lastAnalysisResult && lastAnalysisResult.confidence !== undefined">
        <text class="confidence-text">识别置信度: {{ lastAnalysisResult.confidence }}%</text>
        <text class="description-text">{{ lastAnalysisResult.description }}</text>
      </view>
    </view>

    <!-- 当前学习状态 -->
    <view class="status-container">
      <view class="status-header">
        <text class="status-title">当前学习状态</text>
        <text class="status-time">监测时长: {{ formattedMonitoringTime }}</text>
      </view>
      <view class="status-details">
        <text class="status-label">专注度</text>
        <text class="status-value" :class="{ 'good': focusLevel >= 70, 'warning': focusLevel < 70 }">
          {{ focusLevel }}%
        </text>
      </view>
      <view class="status-details">
        <text class="status-label">疲劳指数</text>
        <text class="status-value" :class="{ 'good': fatigueLevel < 50, 'warning': fatigueLevel >= 50 }">
          {{ fatigueLevel }}%
        </text>
      </view>
      <button class="start-button" @click="toggleMonitoring">
        {{ isMonitoring ? '停止监测' : '开始监测' }}
      </button>
    </view>

    <!-- AI建议 -->
    <view class="suggestion-container">
      <view class="section-title">
        <text>AI学习建议</text>
      </view>
      <view class="suggestion-content" v-if="suggestion">
        <text class="suggestion-text">{{ suggestion }}</text>
      </view>
      <view class="no-suggestion" v-else>
        <text>停止监测后将提供建议</text>
      </view>
    </view>

    <!-- 学习行为记录 -->
    <view class="record-container">
      <view class="record-header">
        <text class="section-title">学习行为记录</text>
        <button class="clear-button" @click="clearRecords">清除记录</button>
      </view>
      <view class="record-list">
        <view class="record-item" v-for="(record, index) in records" :key="index">
          <text class="record-time">{{ record.time }}</text>
          <text class="record-detail">{{ record.detail }}</text>
        </view>
        <view class="no-records" v-if="records.length === 0">
          <text>暂无记录</text>
        </view>
      </view>
    </view>

    <!-- 使用方法弹窗 -->
    <view class="modal" v-if="showHelpModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">使用方法</text>
          <text class="close-button" @click="showHelpModal = false">×</text>
        </view>
        <view class="modal-body">
          <text class="help-text">
            1. 点击“开始监测”启动学习状态监测，监测时长精确到秒。\n
            2. 每分钟更新一次专注度和疲劳指数。\n
            3. 点击“停止监测”结束并生成基于平均值的建议。\n
            4. 建议会记录到“学习行为记录”中，永久保存。\n
            5. 点击“清除记录”可删除所有记录。\n
            6. 若监测不足1分钟，使用当前数据生成建议。
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onUnmounted, computed, onMounted, nextTick } from 'vue';
import { analyzeStudyState, analyzeStudyStateFromBase64, isGeminiServiceAvailable } from '@/utils/geminiService.js';

// 监测状态
const isMonitoring = ref(false);
const monitoringTime = ref(0); // 秒计时
const focusLevel = ref(0);
const fatigueLevel = ref(0);
let timer = null;

// AI识别相关状态
const useAI = ref(true); // 是否使用AI识别
const isAnalyzing = ref(false); // 是否正在分析图像
const lastAnalysisResult = ref(null); // 最后一次分析结果

// H5摄像头相关状态
const cameraSupported = ref(false); // 浏览器是否支持摄像头
const cameraStream = ref(null); // 摄像头流
const videoElement = ref(null); // video元素引用
const canvasElement = ref(null); // canvas元素引用
const autoCapture = ref(true); // 是否自动拍照
let autoCaptureTimer = null; // 自动拍照定时器

// 记录每分钟的专注度和疲劳指数
const focusLevels = ref([]);
const fatigueLevels = ref([]);

// AI建议
const suggestion = ref('');

// 学习行为记录
const records = ref([]);

// 使用方法弹窗
const showHelpModal = ref(false);

// 加载数据
const loadData = () => {
  try {
    const storedRecords = uni.getStorageSync('monitorRecords') || '[]';
    records.value = JSON.parse(storedRecords);
  } catch (e) {
    console.error('加载记录失败', e);
    records.value = [];
  }
};

// 保存数据
const saveData = () => {
  try {
    uni.setStorageSync('monitorRecords', JSON.stringify(records.value));
  } catch (e) {
    console.error('保存记录失败', e);
  }
};

// 检查浏览器摄像头支持
const checkCameraSupport = () => {
  if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
    cameraSupported.value = true;
    return true;
  } else {
    cameraSupported.value = false;
    console.warn('当前浏览器不支持摄像头功能');
    return false;
  }
};

// 初始化H5摄像头
const initH5Camera = async () => {
  if (!checkCameraSupport()) {
    return false;
  }

  try {
    const constraints = {
      video: {
        width: { ideal: 640 },
        height: { ideal: 480 },
        facingMode: 'user' // 前置摄像头
      },
      audio: false
    };

    const stream = await navigator.mediaDevices.getUserMedia(constraints);
    cameraStream.value = stream;

    // 等待多个tick确保DOM完全渲染
    await nextTick();
    await new Promise(resolve => setTimeout(resolve, 100));

    // 多次尝试获取video元素
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      await nextTick();

      if (videoElement.value) {
        try {
          const video = videoElement.value;
          video.srcObject = stream;

          // 等待video元素加载完成
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('Video加载超时'));
            }, 5000);

            const onLoadedData = () => {
              clearTimeout(timeout);
              video.removeEventListener('loadeddata', onLoadedData);
              video.removeEventListener('error', onError);
              resolve();
            };

            const onError = (error) => {
              clearTimeout(timeout);
              video.removeEventListener('loadeddata', onLoadedData);
              video.removeEventListener('error', onError);
              reject(error);
            };

            video.addEventListener('loadeddata', onLoadedData);
            video.addEventListener('error', onError);

            // 如果video已经加载完成
            if (video.readyState >= 2) {
              onLoadedData();
            }
          });

          console.log('H5摄像头初始化成功');
          return true;
        } catch (videoError) {
          console.error('设置video srcObject失败:', videoError);
          attempts++;
          await new Promise(resolve => setTimeout(resolve, 100));
          continue;
        }
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    console.error('video元素未找到，尝试次数:', attempts);
    // 停止流
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    cameraStream.value = null;
    return false;

  } catch (error) {
    console.error('摄像头初始化失败:', error);
    cameraSupported.value = false;

    let errorMessage = '摄像头启动失败';
    if (error.name === 'NotAllowedError') {
      errorMessage = '摄像头权限被拒绝，请允许访问摄像头';
    } else if (error.name === 'NotFoundError') {
      errorMessage = '未找到摄像头设备';
    } else if (error.name === 'NotReadableError') {
      errorMessage = '摄像头被其他应用占用';
    }

    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });
    return false;
  }
};

// 停止摄像头
const stopCamera = () => {
  try {
    if (cameraStream.value) {
      const tracks = cameraStream.value.getTracks();
      tracks.forEach(track => {
        try {
          track.stop();
        } catch (error) {
          console.warn('停止track失败:', error);
        }
      });
      cameraStream.value = null;
      console.log('摄像头已停止');
    }

    // 清理video元素
    if (videoElement.value) {
      try {
        videoElement.value.srcObject = null;
      } catch (error) {
        console.warn('清理video元素失败:', error);
      }
    }
  } catch (error) {
    console.error('停止摄像头时出错:', error);
  }
};

// 自动拍照开关切换
const onAutoCaptureChange = (event) => {
  autoCapture.value = event.detail.value;

  if (autoCapture.value && isMonitoring.value && cameraStream.value) {
    startAutoCapture();
  } else {
    stopAutoCapture();
  }

  uni.showToast({
    title: autoCapture.value ? '已开启自动拍照' : '已关闭自动拍照',
    icon: 'success'
  });
};

// AI开关切换
const onAIToggleChange = (event) => {
  useAI.value = event.detail.value;

  if (!useAI.value) {
    lastAnalysisResult.value = null;
  }

  uni.showToast({
    title: useAI.value ? '已开启AI识别' : '已关闭AI识别',
    icon: 'success'
  });
};

// 开始自动拍照
const startAutoCapture = () => {
  if (autoCaptureTimer) {
    clearInterval(autoCaptureTimer);
  }

  if (autoCapture.value && useAI.value && cameraStream.value) {
    console.log('开始自动拍照，间隔20秒');
    autoCaptureTimer = setInterval(() => {
      if (isMonitoring.value && cameraStream.value && !isAnalyzing.value) {
        console.log('执行自动拍照');
        captureImage();
      }
    }, 20000); // 20秒间隔
  }
};

// 停止自动拍照
const stopAutoCapture = () => {
  if (autoCaptureTimer) {
    clearInterval(autoCaptureTimer);
    autoCaptureTimer = null;
    console.log('停止自动拍照');
  }
};

// H5拍照功能
const captureImageFromVideo = async () => {
  try {
    if (!videoElement.value || !canvasElement.value) {
      console.error('video或canvas元素未找到');
      return null;
    }

    const video = videoElement.value;
    const canvas = canvasElement.value;

    // 检查video是否已经加载
    if (video.readyState < 2) {
      console.error('video尚未准备就绪，readyState:', video.readyState);
      return null;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('无法获取canvas上下文');
      return null;
    }

    // 等待video尺寸可用
    let waitAttempts = 0;
    while ((!video.videoWidth || !video.videoHeight) && waitAttempts < 20) {
      await new Promise(resolve => setTimeout(resolve, 100));
      waitAttempts++;
    }

    // 检查video尺寸
    if (!video.videoWidth || !video.videoHeight) {
      console.error('video尺寸无效，videoWidth:', video.videoWidth, 'videoHeight:', video.videoHeight);
      // 使用默认尺寸
      const defaultWidth = 640;
      const defaultHeight = 480;
      console.log('使用默认尺寸:', defaultWidth, 'x', defaultHeight);

      canvas.width = defaultWidth;
      canvas.height = defaultHeight;

      // 尝试绘制，即使尺寸可能不准确
      try {
        ctx.drawImage(video, 0, 0, defaultWidth, defaultHeight);
      } catch (drawError) {
        console.error('绘制失败:', drawError);
        return null;
      }
    } else {
      // 设置canvas尺寸与video一致
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // 绘制当前视频帧到canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    }

    // 转换为base64
    const dataURL = canvas.toDataURL('image/jpeg', 0.8);

    if (!dataURL || dataURL === 'data:,') {
      console.error('canvas转换失败');
      return null;
    }

    return dataURL;
  } catch (error) {
    console.error('拍照过程出错:', error);
    return null;
  }
};

// 将base64转换为临时文件路径（用于兼容原有的AI分析接口）
const base64ToTempFile = async (base64Data) => {
  try {
    // 移除data:image/jpeg;base64,前缀
    const base64 = base64Data.replace(/^data:image\/jpeg;base64,/, '');

    // 在H5环境中，我们直接返回base64数据
    // 因为geminiService.js中的imageToBase64函数需要适配
    return base64;
  } catch (error) {
    console.error('base64转换失败:', error);
    throw error;
  }
};

// 拍照并分析图像
const captureImage = async () => {
  if (isAnalyzing.value) return;

  if (!useAI.value) {
    // 如果关闭AI识别，直接生成随机数据
    updateRandomData();
    return;
  }

  if (!cameraStream.value) {
    uni.showToast({
      title: '摄像头未就绪',
      icon: 'none'
    });
    return;
  }

  try {
    isAnalyzing.value = true;

    // H5环境下使用canvas拍照
    const imageData = captureImageFromVideo();
    if (!imageData) {
      throw new Error('拍照失败');
    }

    // 转换为临时文件格式
    const base64Data = await base64ToTempFile(imageData);

    // 调用AI分析（需要修改geminiService以支持直接传入base64）
    const result = await analyzeStudyStateFromBase64(base64Data);

    if (result.success) {
      // 更新数据
      focusLevel.value = Math.round(result.data.focusLevel);
      fatigueLevel.value = Math.round(result.data.fatigueLevel);
      lastAnalysisResult.value = result.data;

      // 记录到历史数据
      if (focusLevels.value.length > 0) {
        focusLevels.value[focusLevels.value.length - 1] = focusLevel.value;
        fatigueLevels.value[fatigueLevels.value.length - 1] = fatigueLevel.value;
      }

      uni.showToast({
        title: '分析完成',
        icon: 'success'
      });
    } else {
      // AI分析失败，使用随机数据
      updateRandomData();
      uni.showToast({
        title: result.error || '分析失败，使用随机数据',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('拍照分析错误:', error);
    updateRandomData();
    uni.showToast({
      title: '拍照失败，使用随机数据',
      icon: 'none'
    });
  } finally {
    isAnalyzing.value = false;
  }
};

// 更新随机数据（降级方案）
const updateRandomData = () => {
  focusLevel.value = Math.round(Math.random() * 100);
  fatigueLevel.value = Math.round(Math.random() * 100);

  // 更新历史数据
  if (focusLevels.value.length > 0) {
    focusLevels.value[focusLevels.value.length - 1] = focusLevel.value;
    fatigueLevels.value[fatigueLevels.value.length - 1] = fatigueLevel.value;
  }
};

// 格式化监测时长为 HH:MM:SS
const formattedMonitoringTime = computed(() => {
  const hours = Math.floor(monitoringTime.value / 3600);
  const minutes = Math.floor((monitoringTime.value % 3600) / 60);
  const seconds = monitoringTime.value % 60;
  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
});

// 开始/停止监测
const toggleMonitoring = async () => {
  if (isMonitoring.value) {
    // 停止监测
    clearInterval(timer);
    stopAutoCapture(); // 停止自动拍照
    stopCamera(); // 停止摄像头
    generateSuggestion();
    addRecord();
    focusLevels.value = [];
    fatigueLevels.value = [];
    lastAnalysisResult.value = null; // 清空分析结果
    isMonitoring.value = false;
  } else {
    // 开始监测
    isMonitoring.value = true; // 先设置状态，触发DOM更新
    monitoringTime.value = 0;

    // 等待DOM更新
    await nextTick();
    await new Promise(resolve => setTimeout(resolve, 200));

    // 初始化摄像头（无论是否使用AI都启动摄像头用于实时显示）
    try {
      const cameraInitialized = await initH5Camera();
      if (!cameraInitialized) {
        uni.showToast({
          title: '摄像头初始化失败，将使用随机数据',
          icon: 'none',
          duration: 3000
        });
      } else {
        // 摄像头初始化成功，启动自动拍照
        if (autoCapture.value && useAI.value) {
          startAutoCapture();
        }
      }
    } catch (error) {
      console.error('摄像头初始化异常:', error);
      uni.showToast({
        title: '摄像头初始化异常，将使用随机数据',
        icon: 'none',
        duration: 3000
      });
    }

    // 初始化数据
    if (useAI.value && isGeminiServiceAvailable() && cameraStream.value) {
      // 如果使用AI且服务可用，初始化为0，等待第一次拍照分析
      focusLevel.value = 0;
      fatigueLevel.value = 0;

      if (autoCapture.value) {
        uni.showToast({
          title: '摄像头已启动，将每20秒自动拍照分析',
          icon: 'success',
          duration: 3000
        });
      } else {
        uni.showToast({
          title: '摄像头已启动，请开启自动拍照或手动拍照',
          icon: 'none',
          duration: 3000
        });
      }
    } else {
      // 否则使用随机值
      focusLevel.value = Math.round(Math.random() * 100);
      fatigueLevel.value = Math.round(Math.random() * 100);
    }

    focusLevels.value = [focusLevel.value];
    fatigueLevels.value = [fatigueLevel.value];
    suggestion.value = ''; // 清空建议

    timer = setInterval(() => {
      monitoringTime.value += 1;
      if (monitoringTime.value % 60 === 0) { // 每分钟更新一次
        if (!useAI.value || !isGeminiServiceAvailable() || !cameraStream.value || !autoCapture.value) {
          // 如果不使用AI或服务不可用或未开启自动拍照，生成随机数据
          focusLevel.value = Math.round(Math.random() * 100);
          fatigueLevel.value = Math.round(Math.random() * 100);
          focusLevels.value.push(focusLevel.value);
          fatigueLevels.value.push(fatigueLevel.value);
        } else {
          // 使用AI且开启自动拍照时，添加新的数据点（由自动拍照更新）
          focusLevels.value.push(focusLevel.value);
          fatigueLevels.value.push(fatigueLevel.value);
        }
      }
    }, 1000); // 每秒计时
  }
};

// 计算平均值并生成AI建议
const generateSuggestion = () => {
  const avgFocus = focusLevels.value.length > 0
    ? focusLevels.value.reduce((sum, val) => sum + val, 0) / focusLevels.value.length
    : focusLevel.value; // 若不足1分钟，使用当前值
  const avgFatigue = fatigueLevels.value.length > 0
    ? fatigueLevels.value.reduce((sum, val) => sum + val, 0) / fatigueLevels.value.length
    : fatigueLevel.value; // 若不足1分钟，使用当前值
  const avgScore = (avgFocus + (100 - avgFatigue)) / 2;

  if (avgScore < 30) {
    suggestion.value = `平均专注度 ${avgFocus.toFixed(0)}%，疲劳指数 ${avgFatigue.toFixed(0)}%，学习状态较差，建议休息15分钟并调整学习内容。`;
  } else if (avgScore < 50) {
    suggestion.value = `平均专注度 ${avgFocus.toFixed(0)}%，疲劳指数 ${avgFatigue.toFixed(0)}%，状态不佳，建议休息10分钟或切换轻松任务。`;
  } else if (avgScore < 70) {
    suggestion.value = `平均专注度 ${avgFocus.toFixed(0)}%，疲劳指数 ${avgFatigue.toFixed(0)}%，状态一般，建议休息5分钟或调整学习节奏。`;
  } else {
    suggestion.value = `平均专注度 ${avgFocus.toFixed(0)}%，疲劳指数 ${avgFatigue.toFixed(0)}%，状态良好，请继续保持专注并合理安排时间！`;
  }
};

// 添加行为记录
const addRecord = () => {
  const now = new Date();
  const time = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  records.value.unshift({ time, detail: suggestion.value });
  saveData();
};

// 清除记录
const clearRecords = () => {
  uni.showModal({
    title: '确认清除',
    content: '确定要清除所有学习行为记录吗？',
    success: (res) => {
      if (res.confirm) {
        records.value = [];
        saveData();
        uni.showToast({ title: '记录已清除', icon: 'success' });
      }
    }
  });
};

// 显示使用方法
const showHelp = () => {
  showHelpModal.value = true;
};

// 全局错误处理
const handleGlobalError = (error, context = '') => {
  console.error(`全局错误 [${context}]:`, error);

  // 忽略特定的Chrome扩展错误
  if (error.message && error.message.includes('message port closed')) {
    return; // 静默处理Chrome扩展错误
  }

  // 其他错误的处理逻辑
  if (context.includes('摄像头')) {
    uni.showToast({
      title: '摄像头功能异常',
      icon: 'none',
      duration: 2000
    });
  }
};

// 初始化检查
const initializeApp = async () => {
  try {
    // 设置全局错误处理
    window.addEventListener('error', (event) => {
      handleGlobalError(event.error, '全局异常');
    });

    window.addEventListener('unhandledrejection', (event) => {
      handleGlobalError(event.reason, 'Promise异常');
    });

    // 加载数据
    loadData();

    // 检查摄像头支持
    checkCameraSupport();

    console.log('应用初始化完成，摄像头支持:', cameraSupported.value);
  } catch (error) {
    handleGlobalError(error, '应用初始化');
  }
};

// 组件挂载时初始化
onMounted(() => {
  initializeApp();
});

// 组件卸载时清理定时器和摄像头
onUnmounted(() => {
  if (timer) clearInterval(timer);
  stopAutoCapture(); // 清理自动拍照定时器
  stopCamera(); // 清理摄像头资源
});
</script>

<style lang="scss">
.monitor {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  .help-button {
    font-size: 36rpx;
    color: #007aff;
    padding: 10rpx;
  }
}

.status-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    .status-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    .status-time {
      font-size: 28rpx;
      color: #666;
    }
  }
  .status-details {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 0;
    border-bottom: 2rpx solid #eee;
    &:last-child {
      border-bottom: none;
    }
    .status-label {
      font-size: 28rpx;
      color: #666;
    }
    .status-value {
      font-size: 32rpx;
      font-weight: bold;
      &.good {
        color: #28a745;
      }
      &.warning {
        color: #ff3b30;
      }
    }
  }
  .start-button {
    width: 100%;
    height: 80rpx;
    margin-top: 30rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
  }
}

.suggestion-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .section-title {
    margin-bottom: 20rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  .suggestion-content {
    .suggestion-text {
      font-size: 28rpx;
      color: #333;
      line-height: 40rpx;
    }
  }
  .no-suggestion {
    text-align: center;
    font-size: 28rpx;
    color: #666;
    padding: 20rpx;
  }
}

.record-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    .clear-button {
      height: 60rpx;
      padding: 0 20rpx;
      background-color: #ff3b30;
      color: #fff;
      border: none;
      border-radius: 10rpx;
      font-size: 28rpx;
    }
  }
  .record-list {
    .record-item {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #eee;
      &:last-child {
        border-bottom: none;
      }
      .record-time {
        font-size: 28rpx;
        color: #666;
      }
      .record-detail {
        font-size: 28rpx;
        color: #333;
      }
    }
    .no-records {
      text-align: center;
      font-size: 28rpx;
      color: #666;
      padding: 40rpx;
    }
  }
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #eee;
  .modal-title {
    font-size: 36rpx;
    font-weight: bold;
  }
  .close-button {
    font-size: 48rpx;
    color: #999;
  }
}

// 摄像头相关样式
.camera-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .camera-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .camera-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .camera-status {
      font-size: 28rpx;
      color: #28a745;

      &.analyzing {
        color: #ff9500;
      }
    }
  }

  .camera-preview-container {
    position: relative;
    width: 100%;
    height: 400rpx;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    background-color: #000;
  }

  .camera-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10rpx;
  }

  .camera-fallback, .camera-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    border: 2rpx dashed #ccc;
    border-radius: 10rpx;
  }

  .fallback-text, .loading-text {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }

  .fallback-hint {
    font-size: 24rpx;
    color: #999;
    text-align: center;
    line-height: 32rpx;
  }

  .capture-canvas {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .camera-controls {
    margin-bottom: 20rpx;

    .control-group {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15rpx;
    }

    .ai-toggle, .auto-toggle {
      display: flex;
      align-items: center;

      .toggle-label {
        font-size: 28rpx;
        color: #333;
        margin-right: 10rpx;
      }
    }

    .status-info {
      text-align: center;
      padding: 10rpx;
      background-color: #f8f9fa;
      border-radius: 8rpx;

      .status-text {
        font-size: 26rpx;
        color: #666;
      }
    }
  }

  .confidence-info {
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 10rpx;

    .confidence-text {
      font-size: 28rpx;
      color: #007aff;
      margin-bottom: 10rpx;
      display: block;
    }

    .description-text {
      font-size: 26rpx;
      color: #666;
      line-height: 36rpx;
    }
  }
}

.modal-body {
  padding: 30rpx;
  .help-text {
    font-size: 28rpx;
    color: #333;
    line-height: 40rpx;
    white-space: pre-wrap;
  }
}
</style>