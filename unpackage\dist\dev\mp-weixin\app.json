{"pages": ["pages/index/index", "pages/society/society", "pages/achievement/achievement", "pages/enter/enter", "pages/plan/plan", "pages/daily/daily", "pages/monitor/monitor", "pages/planmanagement/planmanagement", "pages/timereminder/timereminder", "pages/dailypunch/dailypunch", "pages/schedule/schedule", "pages/dataanalysis/dataanalysis", "pages/evaluate/evaluate", "pages/achievementwall/achievementwall", "pages/saying/saying"], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#FFF5EE", "backgroundColor": "#F8F8F8", "enablePullDownRefresh": true}, "tabBar": {"list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/society/society", "text": "社区"}, {"pagePath": "pages/enter/enter", "text": "我的"}]}, "usingComponents": {}}