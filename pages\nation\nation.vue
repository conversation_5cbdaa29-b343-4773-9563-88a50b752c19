<template>
  <view class="nation-page">
    <!-- 搜索区域 -->
    <view class="search-section">
      <input
        class="search-input"
        v-model="searchQuery"
        :placeholder="searchMode === 'zh' ? '用中文搜索' : 'Search in English'"
        @input="filterRegions"
      />
      <view class="search-mode-toggle">
        <text
          :class="{ active: searchMode === 'zh' }"
          @click="switchSearchMode('zh')"
        >
          用中文搜索
        </text>
        <text
          :class="{ active: searchMode === 'en' }"
          @click="switchSearchMode('en')"
        >
          Search in English
        </text>
      </view>
    </view>

    <!-- 地区选择列表 -->
    <scroll-view class="region-list" scroll-y>
      <view v-for="(region, index) in filteredRegions" :key="index" class="button-container">
        <button class="custom-button" @click="selectRegion(region)">
          <text class="button-icon">{{ region.flag }}</text>
          <text class="button-text">
            {{ region.name }} {{ region.enName }} {{ region.dialCode }}
          </text>
        </button>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const regions = ref([
  { name: '中国', flag: '🇨🇳', enName: 'China', code: 'CN', dialCode: '+86' },
  { name: '阿布哈兹', flag: '🇬🇪', enName: 'Abkhazia', code: 'GE', dialCode: '+995' },
  { name: '阿富汗', flag: '🇦🇫', enName: 'Afghanistan', code: 'AF', dialCode: '+93' },
  { name: '阿尔巴尼亚', flag: '🇦🇱', enName: 'Albania', code: 'AL', dialCode: '+355' },
  { name: '阿尔及利亚', flag: '🇩🇿', enName: 'Algeria', code: 'DZ', dialCode: '+213' },
  { name: '安道尔', flag: '🇦🇩', enName: 'Andorra', code: 'AD', dialCode: '+376' },
  { name: '安哥拉', flag: '🇦🇴', enName: 'Angola', code: 'AO', dialCode: '+244' },
  { name: '安提瓜和巴布达', flag: '🇦🇬', enName: 'Antigua and Barbuda', code: 'AG', dialCode: '+1-268' },
  { name: '阿根廷', flag: '🇦🇷', enName: 'Argentina', code: 'AR', dialCode: '+54' },
  { name: '亚美尼亚', flag: '🇦🇲', enName: 'Armenia', code: 'AM', dialCode: '+374' },
  { name: '阿鲁巴', flag: '🇦🇼', enName: 'Aruba', code: 'AW', dialCode: '+297' },
  { name: '澳大利亚', flag: '🇦🇺', enName: 'Australia', code: 'AU', dialCode: '+61' },
  { name: '奥地利', flag: '🇦🇹', enName: 'Austria', code: 'AT', dialCode: '+43' },
  { name: '阿塞拜疆', flag: '🇦🇿', enName: 'Azerbaijan', code: 'AZ', dialCode: '+994' },
  { name: '巴哈马', flag: '🇧🇸', enName: 'Bahamas', code: 'BS', dialCode: '+1-242' },
  { name: '巴林', flag: '🇧🇭', enName: 'Bahrain', code: 'BH', dialCode: '+973' },
  { name: '孟加拉国', flag: '🇧🇩', enName: 'Bangladesh', code: 'BD', dialCode: '+880' },
  { name: '巴巴多斯', flag: '🇧🇧', enName: 'Barbados', code: 'BB', dialCode: '+1-246' },
  { name: '白俄罗斯', flag: '🇧🇾', enName: 'Belarus', code: 'BY', dialCode: '+375' },
  { name: '比利时', flag: '🇧🇪', enName: 'Belgium', code: 'BE', dialCode: '+32' },
  { name: '伯利兹', flag: '🇧🇿', enName: 'Belize', code: 'BZ', dialCode: '+501' },
  { name: '贝宁', flag: '🇧🇯', enName: 'Benin', code: 'BJ', dialCode: '+229' },
  { name: '百慕大', flag: '🇧🇲', enName: 'Bermuda', code: 'BM', dialCode: '+1-441' },
  { name: '不丹', flag: '🇧🇹', enName: 'Bhutan', code: 'BT', dialCode: '+975' },
  { name: '玻利维亚', flag: '🇧🇴', enName: 'Bolivia', code: 'BO', dialCode: '+591' },
  { name: '波斯尼亚和黑塞哥维那', flag: '🇧🇦', enName: 'Bosnia and Herzegovina', code: 'BA', dialCode: '+387' },
  { name: '博茨瓦纳', flag: '🇧🇼', enName: 'Botswana', code: 'BW', dialCode: '+267' },
  { name: '巴西', flag: '🇧🇷', enName: 'Brazil', code: 'BR', dialCode: '+55' },
  { name: '文莱', flag: '🇧🇳', enName: 'Brunei', code: 'BN', dialCode: '+673' },
  { name: '保加利亚', flag: '🇧🇬', enName: 'Bulgaria', code: 'BG', dialCode: '+359' },
  { name: '布基纳法索', flag: '🇧🇫', enName: 'Burkina Faso', code: 'BF', dialCode: '+226' },
  { name: '布隆迪', flag: '🇧🇮', enName: 'Burundi', code: 'BI', dialCode: '+257' },
  { name: '佛得角', flag: '🇨🇻', enName: 'Cabo Verde', code: 'CV', dialCode: '+238' },
  { name: '柬埔寨', flag: '🇰🇭', enName: 'Cambodia', code: 'KH', dialCode: '+855' },
  { name: '喀麦隆', flag: '🇨🇲', enName: 'Cameroon', code: 'CM', dialCode: '+237' },
  { name: '加拿大', flag: '🇨🇦', enName: 'Canada', code: 'CA', dialCode: '+1' },
  { name: '开曼群岛', flag: '🇰🇾', enName: 'Cayman Islands', code: 'KY', dialCode: '+1-345' },
  { name: '中非共和国', flag: '🇨🇫', enName: 'Central African Republic', code: 'CF', dialCode: '+236' },
  { name: '乍得', flag: '🇹🇩', enName: 'Chad', code: 'TD', dialCode: '+235' },
  { name: '智利', flag: '🇨🇱', enName: 'Chile', code: 'CL', dialCode: '+56' },
  { name: '哥伦比亚', flag: '🇨🇴', enName: 'Colombia', code: 'CO', dialCode: '+57' },
  { name: '科摩罗', flag: '🇰🇲', enName: 'Comoros', code: 'KM', dialCode: '+269' },
  { name: '刚果（布）', flag: '🇨🇬', enName: 'Congo (Brazzaville)', code: 'CG', dialCode: '+242' },
  { name: '刚果（金）', flag: '🇨🇩', enName: 'Congo (Kinshasa)', code: 'CD', dialCode: '+243' },
  { name: '库克群岛', flag: '🇨🇰', enName: 'Cook Islands', code: 'CK', dialCode: '+682' },
  { name: '哥斯达黎加', flag: '🇨🇷', enName: 'Costa Rica', code: 'CR', dialCode: '+506' },
  { name: '科特迪瓦', flag: '🇨🇮', enName: "Côte d'Ivoire", code: 'CI', dialCode: '+225' },
  { name: '克罗地亚', flag: '🇭🇷', enName: 'Croatia', code: 'HR', dialCode: '+385' },
  { name: '古巴', flag: '🇨🇺', enName: 'Cuba', code: 'CU', dialCode: '+53' },
  { name: '库拉索', flag: '🇨🇼', enName: 'Curaçao', code: 'CW', dialCode: '+599' },
  { name: '塞浦路斯', flag: '🇨🇾', enName: 'Cyprus', code: 'CY', dialCode: '+357' },
  { name: '捷克共和国', flag: '🇨🇿', enName: 'Czech Republic', code: 'CZ', dialCode: '+420' },
  { name: '丹麦', flag: '🇩🇰', enName: 'Denmark', code: 'DK', dialCode: '+45' },
  { name: '吉布提', flag: '🇩🇯', enName: 'Djibouti', code: 'DJ', dialCode: '+253' },
  { name: '多米尼克', flag: '🇩🇲', enName: 'Dominica', code: 'DM', dialCode: '+1-767' },
  { name: '多米尼加共和国', flag: '🇩🇴', enName: 'Dominican Republic', code: 'DO', dialCode: '+1-809' },
  { name: '厄瓜多尔', flag: '🇪🇨', enName: 'Ecuador', code: 'EC', dialCode: '+593' },
  { name: '埃及', flag: '🇪🇬', enName: 'Egypt', code: 'EG', dialCode: '+20' },
  { name: '萨尔瓦多', flag: '🇸🇻', enName: 'El Salvador', code: 'SV', dialCode: '+503' },
  { name: '赤道几内亚', flag: '🇬🇶', enName: 'Equatorial Guinea', code: 'GQ', dialCode: '+240' },
  { name: '厄立特里亚', flag: '🇪🇷', enName: 'Eritrea', code: 'ER', dialCode: '+291' },
  { name: '爱沙尼亚', flag: '🇪🇪', enName: 'Estonia', code: 'EE', dialCode: '+372' },
  { name: '埃斯瓦蒂尼', flag: '🇸🇿', enName: 'Eswatini', code: 'SZ', dialCode: '+268' },
  { name: '埃塞俄比亚', flag: '🇪🇹', enName: 'Ethiopia', code: 'ET', dialCode: '+251' },
  { name: '斐济', flag: '🇫🇯', enName: 'Fiji', code: 'FJ', dialCode: '+679' },
  { name: '芬兰', flag: '🇫🇮', enName: 'Finland', code: 'FI', dialCode: '+358' },
  { name: '法国', flag: '🇫🇷', enName: 'France', code: 'FR', dialCode: '+33' },
  { name: '加蓬', flag: '🇬🇦', enName: 'Gabon', code: 'GA', dialCode: '+241' },
  { name: '冈比亚', flag: '🇬🇲', enName: 'Gambia', code: 'GM', dialCode: '+220' },
  { name: '格鲁吉亚', flag: '🇬🇪', enName: 'Georgia', code: 'GE', dialCode: '+995' },
  { name: '德国', flag: '🇩🇪', enName: 'Germany', code: 'DE', dialCode: '+49' },
  { name: '加纳', flag: '🇬🇭', enName: 'Ghana', code: 'GH', dialCode: '+233' },
  { name: '希腊', flag: '🇬🇷', enName: 'Greece', code: 'GR', dialCode: '+30' },
  { name: '格陵兰', flag: '🇬🇱', enName: 'Greenland', code: 'GL', dialCode: '+299' },
  { name: '格林纳达', flag: '🇬🇩', enName: 'Grenada', code: 'GD', dialCode: '+1-473' },
  { name: '关岛', flag: '🇬🇺', enName: 'Guam', code: 'GU', dialCode: '+1-671' },
  { name: '危地马拉', flag: '🇬🇹', enName: 'Guatemala', code: 'GT', dialCode: '+502' },
  { name: '几内亚', flag: '🇬🇳', enName: 'Guinea', code: 'GN', dialCode: '+224' },
  { name: '几内亚比绍', flag: '🇬🇼', enName: 'Guinea-Bissau', code: 'GW', dialCode: '+245' },
  { name: '圭亚那', flag: '🇬🇾', enName: 'Guyana', code: 'GY', dialCode: '+592' },
  { name: '海地', flag: '🇭🇹', enName: 'Haiti', code: 'HT', dialCode: '+509' },
  { name: '洪都拉斯', flag: '🇭🇳', enName: 'Honduras', code: 'HN', dialCode: '+504' },
  { name: '香港', flag: '🇭🇰', enName: 'Hong Kong', code: 'HK', dialCode: '+852' },
  { name: '匈牙利', flag: '🇭🇺', enName: 'Hungary', code: 'HU', dialCode: '+36' },
  { name: '冰岛', flag: '🇮🇸', enName: 'Iceland', code: 'IS', dialCode: '+354' },
  { name: '印度', flag: '🇮🇳', enName: 'India', code: 'IN', dialCode: '+91' },
  { name: '印度尼西亚', flag: '🇮🇩', enName: 'Indonesia', code: 'ID', dialCode: '+62' },
  { name: '伊朗', flag: '🇮🇷', enName: 'Iran', code: 'IR', dialCode: '+98' },
  { name: '伊拉克', flag: '🇮🇶', enName: 'Iraq', code: 'IQ', dialCode: '+964' },
  { name: '爱尔兰', flag: '🇮🇪', enName: 'Ireland', code: 'IE', dialCode: '+353' },
  { name: '以色列', flag: '🇮🇱', enName: 'Israel', code: 'IL', dialCode: '+972' },
  { name: '意大利', flag: '🇮🇹', enName: 'Italy', code: 'IT', dialCode: '+39' },
  { name: '牙买加', flag: '🇯🇲', enName: 'Jamaica', code: 'JM', dialCode: '+1-876' },
  { name: '日本', flag: '🇯🇵', enName: 'Japan', code: 'JP', dialCode: '+81' },
  { name: '约旦', flag: '🇯🇴', enName: 'Jordan', code: 'JO', dialCode: '+962' },
  { name: '哈萨克斯坦', flag: '🇰🇿', enName: 'Kazakhstan', code: 'KZ', dialCode: '+7' },
  { name: '肯尼亚', flag: '🇰🇪', enName: 'Kenya', code: 'KE', dialCode: '+254' },
  { name: '基里巴斯', flag: '🇰🇮', enName: 'Kiribati', code: 'KI', dialCode: '+686' },
  { name: '科索沃', flag: '🇽🇰', enName: 'Kosovo', code: 'XK', dialCode: '+383' },
  { name: '科威特', flag: '🇰🇼', enName: 'Kuwait', code: 'KW', dialCode: '+965' },
  { name: '吉尔吉斯斯坦', flag: '🇰🇬', enName: 'Kyrgyzstan', code: 'KG', dialCode: '+996' },
  { name: '老挝', flag: '🇱🇦', enName: 'Laos', code: 'LA', dialCode: '+856' },
  { name: '拉脱维亚', flag: '🇱🇻', enName: 'Latvia', code: 'LV', dialCode: '+371' },
  { name: '黎巴嫩', flag: '🇱🇧', enName: 'Lebanon', code: 'LB', dialCode: '+961' },
  { name: '莱索托', flag: '🇱🇸', enName: 'Lesotho', code: 'LS', dialCode: '+266' },
  { name: '利比里亚', flag: '🇱🇷', enName: 'Liberia', code: 'LR', dialCode: '+231' },
  { name: '利比亚', flag: '🇱🇾', enName: 'Libya', code: 'LY', dialCode: '+218' },
  { name: '列支敦士登', flag: '🇱🇮', enName: 'Liechtenstein', code: 'LI', dialCode: '+423' },
  { name: '立陶宛', flag: '🇱🇹', enName: 'Lithuania', code: 'LT', dialCode: '+370' },
  { name: '卢森堡', flag: '🇱🇺', enName: 'Luxembourg', code: 'LU', dialCode: '+352' },
  { name: '澳门', flag: '🇲🇴', enName: 'Macao', code: 'MO', dialCode: '+853' },
  { name: '马达加斯加', flag: '🇲🇬', enName: 'Madagascar', code: 'MG', dialCode: '+261' },
  { name: '马拉维', flag: '🇲🇼', enName: 'Malawi', code: 'MW', dialCode: '+265' },
  { name: '马来西亚', flag: '🇲🇾', enName: 'Malaysia', code: 'MY', dialCode: '+60' },
  { name: '马尔代夫', flag: '🇲🇻', enName: 'Maldives', code: 'MV', dialCode: '+960' },
  { name: '马里', flag: '🇲🇱', enName: 'Mali', code: 'ML', dialCode: '+223' },
  { name: '马耳他', flag: '🇲🇹', enName: 'Malta', code: 'MT', dialCode: '+356' },
  { name: '马绍尔群岛', flag: '🇲🇭', enName: 'Marshall Islands', code: 'MH', dialCode: '+692' },
  { name: '毛里塔尼亚', flag: '🇲🇷', enName: 'Mauritania', code: 'MR', dialCode: '+222' },
  { name: '毛里求斯', flag: '🇲🇺', enName: 'Mauritius', code: 'MU', dialCode: '+230' },
  { name: '墨西哥', flag: '🇲🇽', enName: 'Mexico', code: 'MX', dialCode: '+52' },
  { name: '密克罗尼西亚', flag: '🇫🇲', enName: 'Micronesia', code: 'FM', dialCode: '+691' },
  { name: '摩尔多瓦', flag: '🇲🇩', enName: 'Moldova', code: 'MD', dialCode: '+373' },
  { name: '摩纳哥', flag: '🇲🇨', enName: 'Monaco', code: 'MC', dialCode: '+377' },
  { name: '蒙古', flag: '🇲🇳', enName: 'Mongolia', code: 'MN', dialCode: '+976' },
  { name: '黑山', flag: '🇲🇪', enName: 'Montenegro', code: 'ME', dialCode: '+382' },
  { name: '摩洛哥', flag: '🇲🇦', enName: 'Morocco', code: 'MA', dialCode: '+212' },
  { name: '莫桑比克', flag: '🇲🇿', enName: 'Mozambique', code: 'MZ', dialCode: '+258' },
  { name: '缅甸', flag: '🇲🇲', enName: 'Myanmar', code: 'MM', dialCode: '+95' },
  { name: '纳米比亚', flag: '🇳🇦', enName: 'Namibia', code: 'NA', dialCode: '+264' },
  { name: '瑙鲁', flag: '🇳🇷', enName: 'Nauru', code: 'NR', dialCode: '+674' },
  { name: '尼泊尔', flag: '🇳🇵', enName: 'Nepal', code: 'NP', dialCode: '+977' },
  { name: '荷兰', flag: '🇳🇱', enName: 'Netherlands', code: 'NL', dialCode: '+31' },
  { name: '新西兰', flag: '🇳🇿', enName: 'New Zealand', code: 'NZ', dialCode: '+64' },
  { name: '尼加拉瓜', flag: '🇳🇮', enName: 'Nicaragua', code: 'NI', dialCode: '+505' },
  { name: '尼日尔', flag: '🇳🇪', enName: 'Niger', code: 'NE', dialCode: '+227' },
  { name: '尼日利亚', flag: '🇳🇬', enName: 'Nigeria', code: 'NG', dialCode: '+234' },
  { name: '纽埃', flag: '🇳🇺', enName: 'Niue', code: 'NU', dialCode: '+683' },
  { name: '北马其顿', flag: '🇲🇰', enName: 'North Macedonia', code: 'MK', dialCode: '+389' },
  { name: '北塞浦路斯', flag: '🇹🇷', enName: 'Northern Cyprus', code: 'TR', dialCode: '+90-392' },
  { name: '挪威', flag: '🇳🇴', enName: 'Norway', code: 'NO', dialCode: '+47' },
  { name: '阿曼', flag: '🇴🇲', enName: 'Oman', code: 'OM', dialCode: '+968' },
  { name: '巴基斯坦', flag: '🇵🇰', enName: 'Pakistan', code: 'PK', dialCode: '+92' },
  { name: '帕劳', flag: '🇵🇼', enName: 'Palau', code: 'PW', dialCode: '+680' },
  { name: '巴勒斯坦', flag: '🇵🇸', enName: 'Palestine', code: 'PS', dialCode: '+970' },
  { name: '巴拿马', flag: '🇵🇦', enName: 'Panama', code: 'PA', dialCode: '+507' },
  { name: '巴布亚新几内亚', flag: '🇵🇬', enName: 'Papua New Guinea', code: 'PG', dialCode: '+675' },
  { name: '巴拉圭', flag: '🇵🇾', enName: 'Paraguay', code: 'PY', dialCode: '+595' },
  { name: '秘鲁', flag: '🇵🇪', enName: 'Peru', code: 'PE', dialCode: '+51' },
  { name: '菲律宾', flag: '🇵🇭', enName: 'Philippines', code: 'PH', dialCode: '+63' },
  { name: '波兰', flag: '🇵🇱', enName: 'Poland', code: 'PL', dialCode: '+48' },
  { name: '葡萄牙', flag: '🇵🇹', enName: 'Portugal', code: 'PT', dialCode: '+351' },
  { name: '波多黎各', flag: '🇵🇷', enName: 'Puerto Rico', code: 'PR', dialCode: '+1-787' },
  { name: '卡塔尔', flag: '🇶🇦', enName: 'Qatar', code: 'QA', dialCode: '+974' },
  { name: '罗马尼亚', flag: '🇷🇴', enName: 'Romania', code: 'RO', dialCode: '+40' },
  { name: '俄罗斯', flag: '🇷🇺', enName: 'Russia', code: 'RU', dialCode: '+7' },
  { name: '卢旺达', flag: '🇷🇼', enName: 'Rwanda', code: 'RW', dialCode: '+250' },
  { name: '圣基茨和尼维斯', flag: '🇰🇳', enName: 'Saint Kitts and Nevis', code: 'KN', dialCode: '+1-869' },
  { name: '圣卢西亚', flag: '🇱🇨', enName: 'Saint Lucia', code: 'LC', dialCode: '+1-758' },
  { name: '圣文森特和格林纳丁斯', flag: '🇻🇚', enName: 'Saint Vincent and the Grenadines', code: 'VC', dialCode: '+1-784' },
  { name: '萨摩亚', flag: '🇼🇸', enName: 'Samoa', code: 'WS', dialCode: '+685' },
  { name: '圣马力诺', flag: '🇸🇲', enName: 'San Marino', code: 'SM', dialCode: '+378' },
  { name: '圣多美和普林西比', flag: '🇸🇹', enName: 'São Tomé and Príncipe', code: 'ST', dialCode: '+239' },
  { name: '沙特阿拉伯', flag: '🇸🇦', enName: 'Saudi Arabia', code: 'SA', dialCode: '+966' },
  { name: '塞内加尔', flag: '🇸🇳', enName: 'Senegal', code: 'SN', dialCode: '+221' },
  { name: '塞尔维亚', flag: '🇷🇸', enName: 'Serbia', code: 'RS', dialCode: '+381' },
  { name: '塞舌尔', flag: '🇸🇨', enName: 'Seychelles', code: 'SC', dialCode: '+248' },
  { name: '塞拉利昂', flag: '🇸🇱', enName: 'Sierra Leone', code: 'SL', dialCode: '+232' },
  { name: '新加坡', flag: '🇸🇬', enName: 'Singapore', code: 'SG', dialCode: '+65' },
  { name: '圣马丁（荷兰部分）', flag: '🇸🇽', enName: 'Sint Maarten', code: 'SX', dialCode: '+1-721' },
  { name: '斯洛伐克', flag: '🇸🇰', enName: 'Slovakia', code: 'SK', dialCode: '+421' },
  { name: '斯洛文尼亚', flag: '🇸🇮', enName: 'Slovenia', code: 'SI', dialCode: '+386' },
  { name: '所罗门群岛', flag: '🇸🇧', enName: 'Solomon Islands', code: 'SB', dialCode: '+677' },
  { name: '索马里', flag: '🇸🇴', enName: 'Somalia', code: 'SO', dialCode: '+252' },
  { name: '索马里兰', flag: '🇸🇴', enName: 'Somaliland', code: 'SO', dialCode: '+252' },
  { name: '南非', flag: '🇿🇦', enName: 'South Africa', code: 'ZA', dialCode: '+27' },
  { name: '南奥塞梯', flag: '🇬🇪', enName: 'South Ossetia', code: 'GE', dialCode: '+995' },
  { name: '南苏丹', flag: '🇸🇸', enName: 'South Sudan', code: 'SS', dialCode: '+211' },
  { name: '西班牙', flag: '🇪🇸', enName: 'Spain', code: 'ES', dialCode: '+34' },
  { name: '斯里兰卡', flag: '🇱🇰', enName: 'Sri Lanka', code: 'LK', dialCode: '+94' },
  { name: '苏丹', flag: '🇸🇩', enName: 'Sudan', code: 'SD', dialCode: '+249' },
  { name: '苏里南', flag: '🇸🇷', enName: 'Suriname', code: 'SR', dialCode: '+597' },
  { name: '瑞典', flag: '🇸🇪', enName: 'Sweden', code: 'SE', dialCode: '+46' },
  { name: '瑞士', flag: '🇨🇭', enName: 'Switzerland', code: 'CH', dialCode: '+41' },
  { name: '叙利亚', flag: '🇸🇾', enName: 'Syria', code: 'SY', dialCode: '+963' },
  { name: '台湾', flag: '🇹🇼', enName: 'Taiwan', code: 'TW', dialCode: '+886' },
  { name: '塔吉克斯坦', flag: '🇹🇯', enName: 'Tajikistan', code: 'TJ', dialCode: '+992' },
  { name: '坦桑尼亚', flag: '🇹🇿', enName: 'Tanzania', code: 'TZ', dialCode: '+255' },
  { name: '泰国', flag: '🇹🇭', enName: 'Thailand', code: 'TH', dialCode: '+66' },
  { name: '东帝汶', flag: '🇹🇱', enName: 'Timor-Leste', code: 'TL', dialCode: '+670' },
  { name: '多哥', flag: '🇹🇬', enName: 'Togo', code: 'TG', dialCode: '+228' },
  { name: '托克劳', flag: '🇹🇰', enName: 'Tokelau', code: 'TK', dialCode: '+690' },
  { name: '汤加', flag: '🇹🇴', enName: 'Tonga', code: 'TO', dialCode: '+676' },
  { name: '特立尼达和多巴哥', flag: '🇹🇹', enName: 'Trinidad and Tobago', code: 'TT', dialCode: '+1-868' },
  { name: '突尼斯', flag: '🇹🇳', enName: 'Tunisia', code: 'TN', dialCode: '+216' },
  { name: '土耳其', flag: '🇹🇷', enName: 'Turkey', code: 'TR', dialCode: '+90' },
  { name: '土库曼斯坦', flag: '🇹🇲', enName: 'Turkmenistan', code: 'TM', dialCode: '+993' },
  { name: '图瓦卢', flag: '🇹🇻', enName: 'Tuvalu', code: 'TV', dialCode: '+688' },
  { name: '乌干达', flag: '🇺🇬', enName: 'Uganda', code: 'UG', dialCode: '+256' },
  { name: '乌克兰', flag: '🇺🇦', enName: 'Ukraine', code: 'UA', dialCode: '+380' },
  { name: '阿联酋', flag: '🇦🇪', enName: 'United Arab Emirates', code: 'AE', dialCode: '+971' },
  { name: '英国', flag: '🇬🇧', enName: 'United Kingdom', code: 'GB', dialCode: '+44' },
  { name: '美国', flag: '🇺🇸', enName: 'United States', code: 'US', dialCode: '+1' },
  { name: '乌拉圭', flag: '🇺🇾', enName: 'Uruguay', code: 'UY', dialCode: '+598' },
  { name: '乌兹别克斯坦', flag: '🇺🇿', enName: 'Uzbekistan', code: 'UZ', dialCode: '+998' },
  { name: '瓦努阿图', flag: '🇻🇺', enName: 'Vanuatu', code: 'VU', dialCode: '+678' },
  { name: '梵蒂冈', flag: '🇻🇦', enName: 'Vatican City', code: 'VA', dialCode: '+39' },
  { name: '委内瑞拉', flag: '🇻🇪', enName: 'Venezuela', code: 'VE', dialCode: '+58' },
  { name: '越南', flag: '🇻🇳', enName: 'Vietnam', code: 'VN', dialCode: '+84' },
  { name: '也门', flag: '🇾🇪', enName: 'Yemen', code: 'YE', dialCode: '+967' },
  { name: '赞比亚', flag: '🇿🇲', enName: 'Zambia', code: 'ZM', dialCode: '+260' },
  { name: '津巴布韦', flag: '🇿🇼', enName: 'Zimbabwe', code: 'ZW', dialCode: '+263' }
]);

const searchQuery = ref('');
const filteredRegions = ref([]);
const searchMode = ref('zh'); // 默认中文搜索模式

// 页面加载时初始化数据
onMounted(() => {
  filteredRegions.value = regions.value;
});

// 切换搜索模式
const switchSearchMode = (mode) => {
  searchMode.value = mode;
  searchQuery.value = ''; // 清空搜索框
  filteredRegions.value = regions.value; // 重置过滤列表
};

// 过滤地区列表
const filterRegions = () => {
  const query = searchQuery.value.trim().toLowerCase();
  if (!query) {
    filteredRegions.value = regions.value;
    return;
  }
  filteredRegions.value = regions.value.filter(region => {
    if (searchMode.value === 'zh') {
      return region.name.toLowerCase().includes(query);
    } else {
      return region.enName.toLowerCase().includes(query);
    }
  });
};

// 选择地区并返回
const selectRegion = (region) => {
  const selected = {
    name: `${region.name}${region.flag}:${region.dialCode}`,
    code: region.dialCode
  };
  uni.navigateBack({
    delta: 1,
    success: () => {
      uni.$emit('updateRegion', selected); // 触发事件通知 login.vue
    }
  });
};
</script>

<style lang="scss">
.nation-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 搜索区域 */
.search-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  .search-input {
    width: 100%;
    height: 80rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .search-mode-toggle {
    display: flex;
    justify-content: space-between;
    text {
      font-size: 24rpx;
      color: #666;
      padding: 10rpx 20rpx;
      border-radius: 10rpx;
      &.active {
        color: #007aff;
        background-color: #f0f8ff;
      }
    }
  }
}

/* 地区列表 */
.region-list {
  flex: 1;
  .button-container {
    margin-bottom: 30rpx;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .custom-button {
    width: 100%;
    height: 80rpx; /* 单行显示，减少高度 */
    background-color: #fff;
    border: 2rpx solid #007aff;
    border-radius: 20rpx;
    color: #333;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    transition: background-color 0.3s, color 0.3s;
    .button-icon {
      margin-right: 20rpx;
      font-size: 40rpx;
    }
    .button-text {
      flex: 1;
      font-size: 28rpx;
      color: #333;
      text-align: center;
    }
    &:active {
      background-color: #f0f8ff;
    }
  }
}
</style>