
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#F8F8F8","enablePullDownRefresh":true,"navigationBar":{"backgroundColor":"#FFF5EE","titleText":"uni-app","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"自律助手","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.45","entryPagePath":"pages/index/index","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#999","selectedColor":"#007aff","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/index/index","text":"首页"},{"pagePath":"pages/society/society","text":"社区"},{"pagePath":"pages/enter/enter","text":"我的"}],"selectedIndex":0,"shown":true},"locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/index/index","meta":{"isQuit":true,"isEntry":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"titleText":"首页","type":"default"},"isNVue":false}},{"path":"pages/society/society","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"navigationBar":{"titleText":"社区","type":"default"},"isNVue":false}},{"path":"pages/achievement/achievement","meta":{"navigationBar":{"titleText":"成就","type":"default"},"isNVue":false}},{"path":"pages/enter/enter","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"navigationBar":{"titleText":"我的","type":"default"},"isNVue":false}},{"path":"pages/plan/plan","meta":{"navigationBar":{"titleText":"计划","type":"default"},"isNVue":false}},{"path":"pages/daily/daily","meta":{"navigationBar":{"titleText":"每日打卡","type":"default"},"isNVue":false}},{"path":"pages/monitor/monitor","meta":{"navigationBar":{"titleText":"学习监测","type":"default"},"isNVue":false}},{"path":"pages/planmanagement/planmanagement","meta":{"navigationBar":{"titleText":"计划管理","type":"default"},"isNVue":false}},{"path":"pages/timereminder/timereminder","meta":{"navigationBar":{"titleText":"时间提醒","type":"default"},"isNVue":false}},{"path":"pages/dailypunch/dailypunch","meta":{"navigationBar":{"titleText":"每日打卡","type":"default"},"isNVue":false}},{"path":"pages/schedule/schedule","meta":{"navigationBar":{"titleText":"进度追踪","type":"default"},"isNVue":false}},{"path":"pages/dataanalysis/dataanalysis","meta":{"navigationBar":{"titleText":"数据分析","type":"default"},"isNVue":false}},{"path":"pages/evaluate/evaluate","meta":{"navigationBar":{"titleText":"评价","type":"default"},"isNVue":false}},{"path":"pages/achievementwall/achievementwall","meta":{"navigationBar":{"titleText":"成就墙","type":"default"},"isNVue":false}},{"path":"pages/saying/saying","meta":{"navigationBar":{"titleText":"激励语录","type":"default"},"isNVue":false}},{"path":"pages/rankinglist/rankinglist","meta":{"navigationBar":{"titleText":"排行榜","type":"default"},"isNVue":false}},{"path":"pages/friends/friends","meta":{"navigationBar":{"titleText":"好友","type":"default"},"isNVue":false}},{"path":"pages/social/social","meta":{"navigationBar":{"titleText":"社交","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  