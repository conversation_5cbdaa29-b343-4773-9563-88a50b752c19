<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">成就系统</text>
      <text class="header-subtitle">记录每一次进步与成长</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <view class="modern-grid three-column">
        <!-- 评价卡片 -->
        <navigator url="/pages/evaluate/evaluate" class="feature-card-nav">
          <view class="feature-card achievement-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">⭐</text>
            </view>
            <view class="card-content">
              <text class="card-title">学习评价</text>
              <text class="card-desc">全面评估表现</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 成就墙卡片 -->
        <navigator url="/pages/achievementwall/achievementwall" class="feature-card-nav">
          <view class="feature-card plan-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">🏆</text>
            </view>
            <view class="card-content">
              <text class="card-title">成就墙</text>
              <text class="card-desc">展示荣誉成就</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 激励语录卡片 -->
        <navigator url="/pages/saying/saying" class="feature-card-nav">
          <view class="feature-card checkin-card fade-in-up">
            <view class="card-icon">
              <text class="icon-text">💡</text>
            </view>
            <view class="card-content">
              <text class="card-title">激励语录</text>
              <text class="card-desc">每日正能量</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
// 使用全局现代化主题样式
.feature-card-nav {
  text-decoration: none;
}

.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}
</style>