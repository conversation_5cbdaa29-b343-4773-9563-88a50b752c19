<template>
  <view class="achievement-page">
    <!-- 功能按钮区域 -->
    <view class="button-section">
      <view class="section-title">
        <text>成就管理</text>
      </view>

      <!-- 评价按钮 -->
      <view class="button-container">
        <navigator url="/pages/evaluate/evaluate">
          <button class="custom-button">
            <text class="button-icon">⭐</text>
            <text class="button-text">评价</text>
          </button>
        </navigator>
      </view>

      <!-- 成就墙按钮 -->
      <view class="button-container">
        <navigator url="/pages/achievementwall/achievementwall">
          <button class="custom-button">
            <text class="button-icon">🏆</text>
            <text class="button-text">成就墙</text>
          </button>
        </navigator>
      </view>

      <!-- 激励语录按钮 -->
      <view class="button-container">
        <navigator url="/pages/saying/saying">
          <button class="custom-button">
            <text class="button-icon">💡</text>
            <text class="button-text">激励语录</text>
          </button>
        </navigator>
      </view>
    </view>
  </view>
</template>

<script setup>
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
.achievement-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 功能按钮区域 */
.button-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    margin-bottom: 30rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

/* 按钮容器样式 */
.button-container {
  margin-bottom: 30rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

/* 自定义按钮样式 */
.custom-button {
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  color: #333;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  transition: background-color 0.3s, color 0.3s;
  
  .button-icon {
    margin-right: 20rpx;
    font-size: 40rpx;
  }
  
  .button-text {
    flex: 1;
    text-align: left;
  }
  
  &:active {
    background-color: #f0f8ff;
  }
}
</style>