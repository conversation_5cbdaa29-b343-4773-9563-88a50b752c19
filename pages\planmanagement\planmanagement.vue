<template>
  <view class="plan-management">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">计划管理</text>
    </view>

    <!-- 添加计划按钮 -->
    <view class="add-plan-container">
      <button class="add-button" @click="showAddModal = true">
        <text class="button-icon">➕</text>
        <text class="button-text">添加新计划</text>
      </button>
    </view>

    <!-- 计划列表 -->
    <view class="plan-list">
      <view class="plan-item" v-for="(plan, index) in plans" :key="index">
        <view class="plan-info">
          <text class="plan-title">{{ plan.title }}</text>
          <text class="plan-time">时间: {{ plan.time }}</text>
          <text class="plan-status" :class="{ 'completed': plan.completed }">
            {{ plan.completed ? '已完成' : '进行中' }}
          </text>
        </view>
        <view class="plan-actions">
          <button class="action-button" @click="togglePlanStatus(index)">
            {{ plan.completed ? '标记未完成' : '标记完成' }}
          </button>
          <button class="action-button delete-button" @click="deletePlan(index)">删除</button>
        </view>
      </view>
      <view class="no-plans" v-if="plans.length === 0">
        <text>暂无计划，快去添加吧！</text>
      </view>
    </view>

    <!-- 添加计划模态框 -->
    <view class="modal" v-if="showAddModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">添加新计划</text>
          <text class="close-button" @click="showAddModal = false">×</text>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">计划名称</text>
            <input type="text" v-model="newPlan.title" class="form-input" placeholder="请输入计划名称" />
          </view>
          <view class="form-group">
            <text class="form-label">计划时间</text>
            <picker mode="date" :value="newPlan.date" @change="onDateChange">
              <view class="form-input">{{ newPlan.date || '选择日期' }}</view>
            </picker>
            <picker mode="time" :value="newPlan.time" @change="onTimeChange">
              <view class="form-input">{{ newPlan.time || '选择时间' }}</view>
            </picker>
          </view>
        </view>
        <view class="modal-footer">
          <button class="submit-button" @click="addPlan">添加</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';
import { migrateAllLocalData } from '@/utils/migrateLocalData.js';

const plans = ref([]);
const showAddModal = ref(false);
const newPlan = reactive({
  title: '',
  date: '',
  time: '',
});

// 加载计划数据（后端）
const loadPlans = async () => {
  try {
    const res = await apiRequest({ url: '/api/plans', method: 'GET' });
    plans.value = (res || []).map(item => ({
      id: item.id,
      title: item.title,
      time: item.remind_time,
      completed: !!item.completed
    }));
  } catch (e) {
    plans.value = [];
  }
};

// 日期选择
const onDateChange = (e) => {
  newPlan.date = e.detail.value;
};

// 时间选择
const onTimeChange = (e) => {
  newPlan.time = e.detail.value;
};

// 添加计划（后端）
const addPlan = async () => {
  if (!newPlan.title || !newPlan.date || !newPlan.time) {
    uni.showToast({ title: '请填写完整信息', icon: 'none' });
    return;
  }
  const fullTime = `${newPlan.date} ${newPlan.time}`;
  try {
    await apiRequest({ url: '/api/plans', method: 'POST', data: { title: newPlan.title, remind_time: fullTime, completed: false, created_at: fullTime } });
    uni.showToast({ title: '添加成功', icon: 'success' });
    showAddModal.value = false;
    newPlan.title = '';
    newPlan.date = '';
    newPlan.time = '';
    await loadPlans();
  } catch (e) {}
};

// 切换计划状态（后端）
const togglePlanStatus = async (index) => {
  const plan = plans.value[index];
  if (!plan) return;
  try {
    await apiRequest({ url: `/api/plans/${plan.id}`, method: 'PUT', data: { completed: !plan.completed } });
    await loadPlans();
  } catch (e) {}
};

// 删除计划（后端）
const deletePlan = async (index) => {
  const plan = plans.value[index];
  if (!plan) return;
  try {
    await apiRequest({ url: `/api/plans/${plan.id}`, method: 'DELETE' });
    await loadPlans();
  } catch (e) {}
};

onMounted(async () => {
  await migrateAllLocalData(); // 首次升级自动迁移本地所有数据到后端
  await loadPlans();
});


onMounted(() => {
  loadPlans();
});
</script>

<style lang="scss">
.plan-management {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.add-plan-container {
  margin-bottom: 30rpx;
  .add-button {
    width: 100%;
    height: 100rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    .button-icon {
      margin-right: 20rpx;
      font-size: 40rpx;
    }
  }
}

.plan-list {
  .plan-item {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .plan-info {
      .plan-title {
        font-size: 32rpx;
        font-weight: bold;
        display: block;
        margin-bottom: 10rpx;
      }
      .plan-time {
        font-size: 28rpx;
        color: #666;
        display: block;
        margin-bottom: 10rpx;
      }
      .plan-status {
        font-size: 28rpx;
        &.completed {
          color: #28a745;
        }
      }
    }

    .plan-actions {
      display: flex;
      gap: 20rpx;
      .action-button {
        height: 60rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        border-radius: 10rpx;
        background-color: #007aff;
        color: #fff;
        border: none;
        &.delete-button {
          background-color: #ff3b30;
        }
      }
    }
  }

  .no-plans {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #eee;
  .modal-title {
    font-size: 36rpx;
    font-weight: bold;
  }
  .close-button {
    font-size: 48rpx;
    color: #999;
  }
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
  .form-label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  .form-input {
    width: 100%;
    height: 80rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    line-height: 80rpx;
    color: #333;
  }
}

.modal-footer {
  padding: 30rpx;
  border-top: 2rpx solid #eee;
  .submit-button {
    width: 100%;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
  }
}
</style>