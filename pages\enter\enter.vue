<template>
  <view class="enter-page">
    <!-- 标题 -->
    <view class="header">
      <text class="app-name">自律助手</text>
    </view>

    <!-- 内容区域 -->
    <view class="content-section">
      <!-- 未登录状态 -->
      <view v-if="!isLoggedIn" class="login-prompt">
        <view class="section-title">
          <text>请先登录以使用更多功能</text>
        </view>
        <view class="button-container">
          <navigator url="/pages/login/login">
            <button class="custom-button" @click="goToLogin">
              <text class="button-icon">🔑</text>
              <text class="button-text">登录 / 注册</text>
            </button>
          </navigator>
        </view>
        <view class="button-container">
          <navigator url="/pages/setting/setting">
            <button class="custom-button" @click="goToSettings">
              <text class="button-icon">⚙️</text>
              <text class="button-text">设置</text>
            </button>
          </navigator>
        </view>
      </view>

      <!-- 已登录状态 -->
      <view v-if="isLoggedIn" class="user-info">
        <!-- 头像和昵称（不可修改） -->
        <view class="user-profile">
          <image class="avatar" :src="userData.avatar" mode="aspectFill" />
          <view class="username-container">
            <text class="flag">{{ getFlag(userData.country) }}</text>
            <text class="username">{{ userData.username }}</text>
          </view>
        </view>

        <!-- 功能按钮 -->
        <view class="button-section">
          <view class="button-container">
            <navigator url="/pages/user/user">
              <button class="custom-button" @click="goToUserCenter">
                <text class="button-icon">👤</text>
                <text class="button-text">个人中心</text>
              </button>
            </navigator>
          </view>
          <view class="button-container">
            <navigator url="/pages/setting/setting">
              <button class="custom-button" @click="goToSettings">
                <text class="button-icon">⚙️</text>
                <text class="button-text">设置</text>
              </button>
            </navigator>
          </view>
          <view class="button-container">
            <button class="custom-button logout-button" @click="handleLogout">
              <text class="button-icon">🚪</text>
              <text class="button-text">退出登录</text>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 用户数据和登录状态
const userData = ref({
  avatar: '/common/images/1.png',
  username: '新用户',
  country: '中国🇨🇳'
});
const isLoggedIn = ref(false);

// 页面加载时检查登录状态和用户数据
onMounted(() => {
  const storedLoginData = uni.getStorageSync('loginData');
  if (storedLoginData && storedLoginData.isLoggedIn) {
    isLoggedIn.value = true;
  }
  const storedUserData = uni.getStorageSync('userData');
  if (storedUserData) {
    userData.value = storedUserData;
  }
});

// 提取国旗
const getFlag = (country) => {
  if (!country) return '';
  const match = country.match(/�.*$/);
  return match ? match[0] : '';
};

// 跳转到登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/login'
  });
};

// 跳转到个人中心页面
const goToUserCenter = () => {
  uni.navigateTo({
    url: '/pages/user/user'
  });
};

// 跳转到设置页面
const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/setting/setting'
  });
};

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        uni.setStorageSync('loginData', { isLoggedIn: false });
        isLoggedIn.value = false;
        uni.showToast({
          title: '已退出登录',
          icon: 'success'
        });
      }
    }
  });
};
</script>

<style lang="scss">
.enter-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 40rpx;
  .app-name {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
  }
}

.content-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.login-prompt {
  .section-title {
    margin-bottom: 30rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-bottom: 20rpx;
  }
  .username-container {
    display: flex;
    align-items: center;
  }
  .flag {
    font-size: 36rpx;
    margin-right: 10rpx;
  }
  .username {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.button-section {
  width: 100%;
}

.button-container {
  margin-bottom: 30rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

.custom-button {
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  color: #333;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  transition: background-color 0.3s, color 0.3s;
  
  .button-icon {
    margin-right: 20rpx;
    font-size: 40rpx;
  }
  
  .button-text {
    flex: 1;
    text-align: left;
  }
  
  &:active {
    background-color: #f0f8ff;
  }
}

.logout-button {
  border-color: #ff4d4f;
  color: #ff4d4f;
  &:active {
    background-color: #fff1f0;
  }
}
</style>