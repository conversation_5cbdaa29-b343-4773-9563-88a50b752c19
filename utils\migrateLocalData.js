// 全量本地数据迁移到后端，首次升级时调用
import { apiRequest } from './request.js';

export async function migrateAllLocalData() {
  // 1. 迁移打卡记录
  try {
    const punchRecords = uni.getStorageSync('punchRecords');
    if (punchRecords) {
      const records = JSON.parse(punchRecords);
      for (const rec of records) {
        if (rec.date && rec.punched) {
          await apiRequest({ url: '/api/punches', method: 'POST', data: { date: rec.date, content: '', type: '每日打卡', created_at: rec.date } });
        }
      }
      uni.removeStorageSync('punchRecords');
    }
  } catch (e) { console.error('迁移打卡记录失败', e); }

  // 2. 迁移计划
  try {
    const plans = uni.getStorageSync('plans');
    if (plans) {
      const planArr = JSON.parse(plans);
      for (const plan of planArr) {
        if (plan.title && plan.time) {
          await apiRequest({ url: '/api/plans', method: 'POST', data: { title: plan.title, remind_time: plan.time, completed: !!plan.completed, created_at: plan.time } });
        }
      }
      uni.removeStorageSync('plans');
    }
  } catch (e) { console.error('迁移计划失败', e); }

  // 3. 迁移任务（如有）
  try {
    const tasks = uni.getStorageSync('tasks');
    if (tasks) {
      const taskArr = JSON.parse(tasks);
      for (const task of taskArr) {
        if (task.title) {
          await apiRequest({ url: '/api/tasks', method: 'POST', data: { title: task.title, description: task.description || '', completed: !!task.completed } });
        }
      }
      uni.removeStorageSync('tasks');
    }
  } catch (e) { console.error('迁移任务失败', e); }

  // 4. 迁移个人资料（如有）
  try {
    const profile = uni.getStorageSync('profile');
    if (profile) {
      const data = JSON.parse(profile);
      await apiRequest({ url: '/api/profile', method: 'POST', data });
      uni.removeStorageSync('profile');
    }
  } catch (e) { console.error('迁移个人资料失败', e); }
}
