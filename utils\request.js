// 统一API请求封装，自动带token
export function apiRequest({ url, method = 'GET', data = {}, header = {} }) {
  const token = uni.getStorageSync('token') || '';
  return new Promise((resolve, reject) => {
    uni.request({
      url: 'http://localhost:5000' + url,
      method,
      data,
      header: {
        'Authorization': token ? 'Bearer ' + token : '',
        'Content-Type': 'application/json',
        ...header
      },
      success: res => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          uni.showToast({ title: res.data.msg || '请求失败', icon: 'none' });
          reject(res.data);
        }
      },
      fail: err => {
        uni.showToast({ title: '网络错误', icon: 'none' });
        reject(err);
      }
    });
  });
}
