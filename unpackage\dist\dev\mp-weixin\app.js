"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/society/society.js";
  "./pages/achievement/achievement.js";
  "./pages/enter/enter.js";
  "./pages/plan/plan.js";
  "./pages/daily/daily.js";
  "./pages/monitor/monitor.js";
  "./pages/planmanagement/planmanagement.js";
  "./pages/timereminder/timereminder.js";
  "./pages/dailypunch/dailypunch.js";
  "./pages/schedule/schedule.js";
  "./pages/dataanalysis/dataanalysis.js";
  "./pages/evaluate/evaluate.js";
  "./pages/achievementwall/achievementwall.js";
  "./pages/saying/saying.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:7", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
