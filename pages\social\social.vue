<template>
  <view class="social">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">社交动态</text>
    </view>

    <!-- 发布动态 -->
    <view class="post-container">
      <textarea class="post-input" v-model="newPost" placeholder="分享你的学习动态..."></textarea>
      <button class="post-button" @click="submitPost">发布</button>
    </view>

    <!-- 动态列表 -->
    <view class="post-list">
      <view class="post-item" v-for="(post, index) in posts" :key="index">
        <view class="post-header">
          <view class="post-avatar">
            <image :src="post.avatar" mode="aspectFill" class="avatar"></image>
          </view>
          <view class="post-info">
            <text class="post-name">{{ post.name }} {{ post.flag }}</text>
            <text class="post-time">{{ post.time }}</text>
          </view>
          <!-- 删除动态按钮，仅用户自己的动态显示 -->
          <button
            class="delete-post-button"
            v-if="post.name === '我'"
            @click="deletePost(index)"
          >
            删除
          </button>
        </view>
        <view class="post-content">
          <text class="post-text">{{ post.text }}</text>
        </view>
        <view class="post-actions">
          <button class="action-button like-button" @click="likePost(index)">
            {{ post.liked ? '取消点赞' : '点赞' }} ({{ post.likes }})
          </button>
          <button class="action-button comment-button" @click="toggleComment(index)">
            {{ post.showComment ? '收起评论' : '评论' }} ({{ post.comments.length }})
          </button>
        </view>
        <!-- 评论区域 -->
        <view class="comment-section" v-if="post.showComment">
          <view class="comment-input-container">
            <input class="comment-input" v-model="newComment[index]" placeholder="输入你的评论..." />
            <button class="comment-submit" @click="submitComment(index)">发送</button>
          </view>
          <view class="comment-list">
            <view class="comment-item" v-for="(comment, cIndex) in post.comments" :key="cIndex">
              <text class="comment-text">{{ comment }}</text>
              <button
                class="delete-comment-button"
                v-if="comment.startsWith('我: ')"
                @click="deleteComment(index, cIndex)"
              >
                删除
              </button>
            </view>
            <view class="no-comments" v-if="post.comments.length === 0">
              <text>暂无评论</text>
            </view>
          </view>
        </view>
      </view>
      <view class="no-posts" v-if="posts.length === 0">
        <text>暂无动态</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';

const posts = ref([]);
const newPost = ref('');
const newComment = ref({});
// 加载动态数据（后端API）
const loadData = async () => {
  const res = await apiRequest({ url: '/api/posts', method: 'GET' });
  posts.value = (res || []).map(post => ({
    ...post,
    name: post.username,
    flag: '', // 可根据用户信息补充国旗
    time: post.created_at,
    text: post.content,
    liked: false, // 可根据后端返回补充是否已点赞
    showComment: false
  }));
};

// 发布动态
const submitPost = async () => {
  if (!newPost.value.trim()) return;
  await apiRequest({ url: '/api/posts', method: 'POST', data: { content: newPost.value } });
  newPost.value = '';
  await loadData();
};

// 点赞
const likePost = async (index) => {
  const post = posts.value[index];
  await apiRequest({ url: `/api/posts/${post.id}/like`, method: 'POST' });
  await loadData();
};

// 删除动态
const deletePost = async (index) => {
  const post = posts.value[index];
  await apiRequest({ url: `/api/posts/${post.id}`, method: 'DELETE' });
  await loadData();
};

// 切换评论显示
const toggleComment = (index) => {
  posts.value[index].showComment = !posts.value[index].showComment;
};

// 提交评论
const submitComment = async (index) => {
  const post = posts.value[index];
  const val = newComment.value[index];
  if (!val || !val.trim()) return;
  await apiRequest({ url: '/api/comments', method: 'POST', data: { post_id: post.id, content: val } });
  newComment.value[index] = '';
  await loadData();
};

// 删除评论（API）
const deleteComment = async (postIndex, commentIndex) => {
  const comment = posts.value[postIndex].comments[commentIndex];
  await apiRequest({ url: `/api/comments/${comment.id}`, method: 'DELETE' });
  await loadData();
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
.social {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.post-container {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .post-input {
    width: 100%;
    height: 200rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .post-button {
    width: 100%;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
  }
}

.post-list {
  .post-item {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    .post-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      position: relative;
      .post-avatar {
        margin-right: 20rpx;
        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 40rpx;
          border: 2rpx solid #007aff;
        }
      }
      .post-info {
        flex: 1;
        .post-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 10rpx;
        }
        .post-time {
          font-size: 26rpx;
          color: #666;
        }
      }
      .delete-post-button {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        height: 50rpx;
        padding: 0 20rpx;
        background-color: #ff3b30;
        color: #fff;
        border: none;
        border-radius: 10rpx;
        font-size: 24rpx;
      }
    }
    .post-content {
      margin-bottom: 20rpx;
      .post-text {
        font-size: 28rpx;
        color: #333;
        line-height: 40rpx;
      }
    }
    .post-actions {
      display: flex;
      justify-content: space-between;
      .action-button {
        width: 48%;
        height: 60rpx;
        border: none;
        border-radius: 10rpx;
        font-size: 28rpx;
        color: #fff;
        &.like-button {
          background-color: #007aff;
        }
        &.comment-button {
          background-color: #28a745;
        }
      }
    }
    .comment-section {
      margin-top: 20rpx;
      .comment-input-container {
        display: flex;
        margin-bottom: 20rpx;
        .comment-input {
          flex: 1;
          height: 60rpx;
          border: 2rpx solid #ddd;
          border-radius: 10rpx;
          padding: 0 20rpx;
          font-size: 26rpx;
          margin-right: 20rpx;
        }
        .comment-submit {
          width: 120rpx;
          height: 60rpx;
          background-color: #007aff;
          color: #fff;
          border: none;
          border-radius: 10rpx;
          font-size: 26rpx;
        }
      }
      .comment-list {
        .comment-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10rpx 0;
          border-bottom: 1rpx solid #eee;
          .comment-text {
            font-size: 26rpx;
            color: #333;
          }
          .delete-comment-button {
            height: 50rpx;
            padding: 0 20rpx;
            background-color: #ff3b30;
            color: #fff;
            border: none;
            border-radius: 10rpx;
            font-size: 24rpx;
          }
        }
        .no-comments {
          text-align: center;
          font-size: 24rpx;
          color: #666;
          padding: 20rpx;
        }
      }
    }
  }
  .no-posts {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}
</style>