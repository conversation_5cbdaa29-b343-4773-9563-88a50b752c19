<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">学习动态</text>
      <text class="header-subtitle">分享学习心得，交流学习经验</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 发布动态卡片 -->
      <view class="modern-card post-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">✍️</text>
          </view>
          <text class="card-title">发布动态</text>
        </view>

        <view class="post-form">
          <textarea
            class="modern-textarea"
            v-model="newPost"
            placeholder="分享你的学习动态..."
          ></textarea>
          <button class="modern-button primary post-btn" @click="submitPost">
            <text class="btn-icon">📤</text>
            <text class="btn-text">发布</text>
          </button>
        </view>
      </view>

      <!-- 动态列表卡片 -->
      <view class="modern-card posts-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">💬</text>
          </view>
          <text class="card-title">学习动态</text>
          <text class="card-subtitle">{{ posts.length }}条动态</text>
        </view>

        <view class="posts-list" v-if="posts.length > 0">
          <view class="post-item" v-for="(post, index) in posts" :key="index">
            <!-- 动态头部 -->
            <view class="post-header">
              <view class="user-avatar">
                <image :src="post.avatar" mode="aspectFill" class="avatar-image"></image>
              </view>
              <view class="user-info">
                <view class="user-name-row">
                  <text class="user-flag">{{ post.flag }}</text>
                  <text class="user-name">{{ post.name }}</text>
                </view>
                <text class="post-time">{{ post.time }}</text>
              </view>
              <button
                class="modern-button danger delete-btn"
                v-if="post.name === '我'"
                @click="deletePost(index)"
              >
                <text class="btn-icon">🗑️</text>
              </button>
            </view>

            <!-- 动态内容 -->
            <view class="post-content">
              <text class="post-text">{{ post.text }}</text>
            </view>

            <!-- 动态操作 -->
            <view class="post-actions">
              <button
                class="action-btn like-btn"
                :class="{ 'liked': post.liked }"
                @click="likePost(index)"
              >
                <text class="btn-icon">{{ post.liked ? '❤️' : '🤍' }}</text>
                <text class="btn-text">{{ post.likes }}</text>
              </button>
              <button class="action-btn comment-btn" @click="toggleComment(index)">
                <text class="btn-icon">💬</text>
                <text class="btn-text">{{ post.comments.length }}</text>
              </button>
            </view>

            <!-- 评论区域 -->
            <view class="comment-section" v-if="post.showComment">
              <view class="comment-input-row">
                <input
                  class="comment-input"
                  v-model="newComment[index]"
                  placeholder="输入你的评论..."
                />
                <button class="modern-button primary comment-submit" @click="submitComment(index)">
                  <text class="btn-icon">📝</text>
                </button>
              </view>

              <view class="comment-list" v-if="post.comments.length > 0">
                <view class="comment-item" v-for="(comment, cIndex) in post.comments" :key="cIndex">
                  <text class="comment-text">{{ comment }}</text>
                  <button
                    class="modern-button danger comment-delete"
                    v-if="comment.startsWith('我: ')"
                    @click="deleteComment(index, cIndex)"
                  >
                    <text class="btn-icon">🗑️</text>
                  </button>
                </view>
              </view>

              <view class="no-comments" v-if="post.comments.length === 0">
                <text class="empty-text">暂无评论，快来抢沙发吧！</text>
              </view>
            </view>
          </view>
        </view>

        <view class="no-posts" v-if="posts.length === 0">
          <view class="empty-icon">
            <text class="icon-text">💬</text>
          </view>
          <text class="empty-text">暂无动态</text>
          <text class="empty-desc">发布第一条学习动态吧！</text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';

const posts = ref([]);
const newPost = ref('');
const newComment = ref({});
// 加载动态数据（后端API）
const loadData = async () => {
  const res = await apiRequest({ url: '/api/posts', method: 'GET' });
  posts.value = (res || []).map(post => ({
    ...post,
    name: post.username,
    flag: '', // 可根据用户信息补充国旗
    time: post.created_at,
    text: post.content,
    liked: false, // 可根据后端返回补充是否已点赞
    showComment: false
  }));
};

// 发布动态
const submitPost = async () => {
  if (!newPost.value.trim()) return;
  await apiRequest({ url: '/api/posts', method: 'POST', data: { content: newPost.value } });
  newPost.value = '';
  await loadData();
};

// 点赞
const likePost = async (index) => {
  const post = posts.value[index];
  await apiRequest({ url: `/api/posts/${post.id}/like`, method: 'POST' });
  await loadData();
};

// 删除动态
const deletePost = async (index) => {
  const post = posts.value[index];
  await apiRequest({ url: `/api/posts/${post.id}`, method: 'DELETE' });
  await loadData();
};

// 切换评论显示
const toggleComment = (index) => {
  posts.value[index].showComment = !posts.value[index].showComment;
};

// 提交评论
const submitComment = async (index) => {
  const post = posts.value[index];
  const val = newComment.value[index];
  if (!val || !val.trim()) return;
  await apiRequest({ url: '/api/comments', method: 'POST', data: { post_id: post.id, content: val } });
  newComment.value[index] = '';
  await loadData();
};

// 删除评论（API）
const deleteComment = async (postIndex, commentIndex) => {
  const comment = posts.value[postIndex].comments[commentIndex];
  await apiRequest({ url: `/api/comments/${comment.id}`, method: 'DELETE' });
  await loadData();
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 发布动态卡片样式
.post-card {
  margin-bottom: 24rpx;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--social-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .post-form {
    .modern-textarea {
      width: 100%;
      height: 160rpx;
      border: 2rpx solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: 20rpx;
      font-size: 26rpx;
      color: var(--gray-800);
      background: var(--white);
      margin-bottom: 20rpx;
      resize: none;
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--social-gradient);
        box-shadow: 0 0 0 4rpx rgba(6, 182, 212, 0.1);
      }

      &::placeholder {
        color: var(--gray-400);
      }
    }

    .post-btn {
      width: 100%;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      .btn-icon {
        font-size: 20rpx;
      }

      .btn-text {
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }
}

// 动态列表卡片样式
.posts-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--social-gradient);
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
      flex: 1;
      margin-left: 16rpx;
    }

    .card-subtitle {
      font-size: 22rpx;
      color: var(--gray-500);
    }
  }

  .posts-list {
    .post-item {
      padding: 24rpx 0;
      border-bottom: 1rpx solid var(--gray-200);

      &:last-child {
        border-bottom: none;
      }

      .post-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .user-avatar {
          margin-right: 16rpx;

          .avatar-image {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            border: 2rpx solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
          }
        }

        .user-info {
          flex: 1;

          .user-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 4rpx;

            .user-flag {
              font-size: 20rpx;
              margin-right: 6rpx;
            }

            .user-name {
              font-size: 26rpx;
              font-weight: 600;
              color: var(--gray-800);
            }
          }

          .post-time {
            font-size: 20rpx;
            color: var(--gray-500);
          }
        }

        .delete-btn {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .btn-icon {
            font-size: 16rpx;
          }
        }
      }

      .post-content {
        margin-bottom: 16rpx;
        padding-left: 76rpx;

        .post-text {
          font-size: 26rpx;
          color: var(--gray-700);
          line-height: 1.5;
        }
      }

      .post-actions {
        display: flex;
        gap: 24rpx;
        padding-left: 76rpx;

        .action-btn {
          display: flex;
          align-items: center;
          gap: 6rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
          background: var(--gray-100);
          border: none;
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
          }

          &.like-btn {
            &.liked {
              background: rgba(255, 59, 48, 0.1);

              .btn-text {
                color: #ff3b30;
              }
            }
          }

          .btn-icon {
            font-size: 18rpx;
          }

          .btn-text {
            font-size: 22rpx;
            color: var(--gray-600);
            font-weight: 500;
          }
        }
      }
      .comment-section {
        margin-top: 16rpx;
        padding-left: 76rpx;

        .comment-input-row {
          display: flex;
          gap: 12rpx;
          margin-bottom: 16rpx;

          .comment-input {
            flex: 1;
            height: 60rpx;
            border: 2rpx solid var(--gray-200);
            border-radius: 30rpx;
            padding: 0 20rpx;
            font-size: 24rpx;
            color: var(--gray-800);
            background: var(--white);
            transition: all 0.3s ease;

            &:focus {
              border-color: var(--social-gradient);
              box-shadow: 0 0 0 4rpx rgba(6, 182, 212, 0.1);
            }

            &::placeholder {
              color: var(--gray-400);
            }
          }

          .comment-submit {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .btn-icon {
              font-size: 18rpx;
            }
          }
        }

        .comment-list {
          .comment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12rpx 0;
            border-bottom: 1rpx solid var(--gray-100);

            &:last-child {
              border-bottom: none;
            }

            .comment-text {
              flex: 1;
              font-size: 24rpx;
              color: var(--gray-700);
              line-height: 1.4;
              margin-right: 12rpx;
            }

            .comment-delete {
              width: 40rpx;
              height: 40rpx;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              .btn-icon {
                font-size: 14rpx;
              }
            }
          }
        }

        .no-comments {
          text-align: center;
          padding: 20rpx;

          .empty-text {
            font-size: 22rpx;
            color: var(--gray-400);
          }
        }
      }
    }
  }

  .no-posts {
    text-align: center;
    padding: 80rpx 20rpx;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 24rpx;
      background: var(--gray-200);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 60rpx;
        color: var(--gray-400);
      }
    }

    .empty-text {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--gray-600);
      margin-bottom: 8rpx;
    }

    .empty-desc {
      display: block;
      font-size: 22rpx;
      color: var(--gray-400);
    }
  }
}
</style>