{"pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/society/society", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/achievement/achievement", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/enter/enter", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/plan/plan", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/daily/daily", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/monitor/monitor", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/planmanagement/planmanagement", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/timereminder/timereminder", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/dailypunch/dailypunch", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/schedule/schedule", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/dataanalysis/dataanalysis", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/evaluate/evaluate", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/achievementwall/achievementwall", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/saying/saying", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/rankinglist/rankinglist", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/friends/friends", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/social/social", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/profile/profile", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/studyrecords/studyrecords", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/setting/setting", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/helpfeedback/helpfeedback", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/login/login", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/nation/nation", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}, {"path": "pages/user/user", "style": {"navigationBarTitleText": " ", "enablePullDownRefresh": false}}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#FFF5EE", "backgroundColor": "#F8F8F8", "enablePullDownRefresh": true}, "tabBar": {"list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/society/society", "text": "社区"}, {"pagePath": "pages/enter/enter", "text": "我的"}]}, "uniIdRouter": {}}