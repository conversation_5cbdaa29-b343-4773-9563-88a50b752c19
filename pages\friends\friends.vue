<template>
  <view class="friends">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">我的好友</text>
    </view>

    <!-- 好友列表 -->
    <view class="friend-list">
      <view class="friend-item" v-for="(friend, index) in friends" :key="index">
        <view class="friend-avatar">
          <image :src="friend.avatar" mode="aspectFill" class="avatar"></image>
        </view>
        <view class="friend-info">
          <text class="friend-name">{{ friend.name }} {{ friend.flag }}</text>
        </view>
        <button class="action-button" @click="removeFriend(index)">删除</button>
      </view>
      <view class="no-friends" v-if="friends.length === 0">
        <text>暂无好友，快去添加吧！</text>
      </view>
    </view>

    <!-- 添加好友 -->
    <view class="add-friend-container">
      <input class="add-input" v-model="newFriendName" placeholder="输入好友用户名" />
      <button class="add-button" @click="addFriend">添加</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import { apiRequest } from '@/utils/request.js';

const friends = ref([]);
const newFriendName = ref('');

// 加载好友数据（API）
const loadData = async () => {
  const res = await apiRequest({ url: '/api/friends', method: 'GET' });
  friends.value = res || [];
};

// 添加好友（API）
const addFriend = async () => {
  if (!newFriendName.value.trim()) {
    uni.showToast({ title: '请输入好友用户名', icon: 'none' });
    return;
  }
  try {
    await apiRequest({ url: '/api/friends', method: 'POST', data: { username: newFriendName.value } });
    newFriendName.value = '';
    uni.showToast({ title: '添加成功', icon: 'success' });
    await loadData();
  } catch (e) {
    uni.showToast({ title: e?.error || '添加失败', icon: 'none' });
  }
};

// 删除好友（API）
const removeFriend = async (index) => {
  const friend = friends.value[index];
  try {
    await apiRequest({ url: `/api/friends/${friend.friend_id}`, method: 'DELETE' });
    uni.showToast({ title: '删除成功', icon: 'success' });
    await loadData();
  } catch (e) {
    uni.showToast({ title: e?.error || '删除失败', icon: 'none' });
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
.friends {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.friend-list {
  margin-bottom: 30rpx;
  .friend-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 20rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    .friend-avatar {
      margin-right: 20rpx;
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 2rpx solid #007aff;
      }
    }
    .friend-info {
      flex: 1;
      .friend-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        display: block;
      }
    }
    .action-button {
      height: 60rpx;
      padding: 0 20rpx;
      background-color: #ff3b30;
      color: #fff;
      border: none;
      border-radius: 10rpx;
      font-size: 28rpx;
    }
  }
  .no-friends {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}

.add-friend-container {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .add-input {
    flex: 1;
    height: 80rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    margin-right: 20rpx;
  }
  .add-button {
    width: 150rpx;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 28rpx;
  }
}
</style>