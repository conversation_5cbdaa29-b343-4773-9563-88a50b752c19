<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">学习好友</text>
      <text class="header-subtitle">添加学习伙伴，共同成长进步</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 添加好友卡片 -->
      <view class="modern-card add-friend-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">➕</text>
          </view>
          <text class="card-title">添加好友</text>
        </view>

        <view class="add-friend-form">
          <input
            class="modern-input"
            v-model="newFriendName"
            placeholder="输入好友用户名"
          />
          <button class="modern-button primary add-btn" @click="addFriend">
            <text class="btn-icon">👥</text>
            <text class="btn-text">添加</text>
          </button>
        </view>
      </view>

      <!-- 好友列表卡片 -->
      <view class="modern-card friends-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">👥</text>
          </view>
          <text class="card-title">我的好友</text>
          <text class="card-subtitle">{{ friends.length }}位好友</text>
        </view>

        <view class="friend-list" v-if="friends.length > 0">
          <view class="friend-item" v-for="(friend, index) in friends" :key="index">
            <view class="friend-avatar">
              <image :src="friend.avatar" mode="aspectFill" class="avatar-image"></image>
              <view class="online-status"></view>
            </view>

            <view class="friend-info">
              <view class="friend-name-row">
                <text class="friend-flag">{{ friend.flag }}</text>
                <text class="friend-name">{{ friend.name }}</text>
              </view>
              <text class="friend-status">学习伙伴</text>
            </view>

            <button class="modern-button danger remove-btn" @click="removeFriend(index)">
              <text class="btn-icon">🗑️</text>
            </button>
          </view>
        </view>

        <view class="no-friends" v-if="friends.length === 0">
          <view class="empty-icon">
            <text class="icon-text">👥</text>
          </view>
          <text class="empty-text">暂无好友</text>
          <text class="empty-desc">添加学习伙伴，一起进步吧！</text>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

import { apiRequest } from '@/utils/request.js';

const friends = ref([]);
const newFriendName = ref('');

// 加载好友数据（API）
const loadData = async () => {
  const res = await apiRequest({ url: '/api/friends', method: 'GET' });
  friends.value = res || [];
};

// 添加好友（API）
const addFriend = async () => {
  if (!newFriendName.value.trim()) {
    uni.showToast({ title: '请输入好友用户名', icon: 'none' });
    return;
  }
  try {
    await apiRequest({ url: '/api/friends', method: 'POST', data: { username: newFriendName.value } });
    newFriendName.value = '';
    uni.showToast({ title: '添加成功', icon: 'success' });
    await loadData();
  } catch (e) {
    uni.showToast({ title: e?.error || '添加失败', icon: 'none' });
  }
};

// 删除好友（API）
const removeFriend = async (index) => {
  const friend = friends.value[index];
  try {
    await apiRequest({ url: `/api/friends/${friend.friend_id}`, method: 'DELETE' });
    uni.showToast({ title: '删除成功', icon: 'success' });
    await loadData();
  } catch (e) {
    uni.showToast({ title: e?.error || '删除失败', icon: 'none' });
  }
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 添加好友卡片样式
.add-friend-card {
  margin-bottom: 24rpx;

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--social-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .add-friend-form {
    display: flex;
    gap: 16rpx;

    .modern-input {
      flex: 1;
      height: 80rpx;
      border: 2rpx solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: 0 24rpx;
      font-size: 28rpx;
      color: var(--gray-800);
      background: var(--white);
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--social-gradient);
        box-shadow: 0 0 0 4rpx rgba(6, 182, 212, 0.1);
      }

      &::placeholder {
        color: var(--gray-400);
      }
    }

    .add-btn {
      width: 140rpx;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      .btn-icon {
        font-size: 18rpx;
      }

      .btn-text {
        font-size: 26rpx;
        font-weight: 500;
      }
    }
  }
}

// 好友列表卡片样式
.friends-card {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--social-gradient);
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
      flex: 1;
      margin-left: 16rpx;
    }

    .card-subtitle {
      font-size: 22rpx;
      color: var(--gray-500);
    }
  }

  .friend-list {
    .friend-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid var(--gray-200);
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
      }

      &:active {
        background: rgba(255, 255, 255, 0.5);
        border-radius: var(--radius-lg);
      }

      .friend-avatar {
        position: relative;
        margin-right: 20rpx;

        .avatar-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          border: 3rpx solid rgba(255, 255, 255, 0.8);
          box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
        }

        .online-status {
          position: absolute;
          bottom: 4rpx;
          right: 4rpx;
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
          background: var(--checkin-gradient);
          border: 2rpx solid white;
        }
      }

      .friend-info {
        flex: 1;

        .friend-name-row {
          display: flex;
          align-items: center;
          margin-bottom: 6rpx;

          .friend-flag {
            font-size: 24rpx;
            margin-right: 8rpx;
          }

          .friend-name {
            font-size: 28rpx;
            font-weight: 600;
            color: var(--gray-800);
          }
        }

        .friend-status {
          font-size: 22rpx;
          color: var(--gray-500);
          padding: 4rpx 12rpx;
          background: var(--social-gradient);
          color: white;
          border-radius: 12rpx;
          display: inline-block;
          font-weight: 500;
        }
      }

      .remove-btn {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn-icon {
          font-size: 18rpx;
        }
      }
    }
  }

  .no-friends {
    text-align: center;
    padding: 80rpx 20rpx;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto 24rpx;
      background: var(--gray-200);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-text {
        font-size: 60rpx;
        color: var(--gray-400);
      }
    }

    .empty-text {
      display: block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--gray-600);
      margin-bottom: 8rpx;
    }

    .empty-desc {
      display: block;
      font-size: 22rpx;
      color: var(--gray-400);
    }
  }
}
</style>