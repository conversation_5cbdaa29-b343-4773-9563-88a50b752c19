<template>
  <view class="daily-punch">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">每日打卡</text>
    </view>

    <!-- 当前日期 -->
    <view class="date-container">
      <text class="current-date">{{ currentDate }}</text>
    </view>

    <!-- 打卡状态 -->
    <view class="punch-status">
      <text class="status-text" :class="{ 'punched': isPunched }">
        {{ isPunched ? '今日已打卡' : '今日未打卡' }}
      </text>
      <button class="punch-button" :disabled="isPunched" @click="handlePunch">
        {{ isPunched ? '已完成' : '立即打卡' }}
      </button>
    </view>

    <!-- 打卡历史 -->
    <view class="punch-history">
      <view class="section-title">
        <text>打卡历史</text>
      </view>
      <view class="history-list">
        <view class="history-item" v-for="(record, index) in punchRecords" :key="index">
          <text class="history-date">{{ record.date }}</text>
          <text class="history-status" :class="{ 'punched': record.punched }">
            {{ record.punched ? '已打卡' : '未打卡' }}
          </text>
        </view>
        <view class="no-records" v-if="punchRecords.length === 0">
          <text>暂无打卡记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { apiRequest } from '@/utils/request.js';

// 当前日期
const currentDate = computed(() => {
  const date = new Date();
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
});

// 打卡状态
const isPunched = ref(false);

// 打卡记录
const punchRecords = ref([]);

// 首次升级时迁移本地旧数据到后端
const migrateLocalPunchRecords = async () => {
  try {
    const storedRecords = uni.getStorageSync('punchRecords');
    if (storedRecords) {
      const records = JSON.parse(storedRecords);
      // 批量上传到后端
      for (const rec of records) {
        if (rec.date && rec.punched) {
          await apiRequest({ url: '/api/punches', method: 'POST', data: { date: rec.date, content: '', type: '每日打卡', created_at: rec.date } });
        }
      }
      uni.removeStorageSync('punchRecords'); // 清除本地数据
    }
  } catch (e) {
    console.error('本地打卡数据迁移失败', e);
  }
};

// 加载打卡历史
const loadPunchRecords = async () => {
  try {
    const res = await apiRequest({ url: '/api/punches', method: 'GET' });
    punchRecords.value = (res || []).map(r => ({ date: r.date, punched: true }));
    // 检查今日是否已打卡
    isPunched.value = punchRecords.value.some(record => record.date === currentDate.value && record.punched);
  } catch (e) {
    punchRecords.value = [];
    isPunched.value = false;
  }
};

// 处理打卡
const handlePunch = async () => {
  try {
    await apiRequest({ url: '/api/punches', method: 'POST', data: { date: currentDate.value, content: '', type: '每日打卡', created_at: currentDate.value } });
    uni.showToast({ title: '打卡成功', icon: 'success' });
    await loadPunchRecords();
  } catch (e) {
    // 错误已全局提示
  }
};

onMounted(async () => {
  await migrateLocalPunchRecords();
  await loadPunchRecords();
});
</script>

<style lang="scss">
.daily-punch {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.date-container {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .current-date {
    font-size: 32rpx;
    color: #333;
  }
}

.punch-status {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .status-text {
    font-size: 36rpx;
    display: block;
    margin-bottom: 20rpx;
    &.punched {
      color: #28a745;
    }
  }
  .punch-button {
    width: 200rpx;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
    &:disabled {
      background-color: #ccc;
    }
  }
}

.punch-history {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .section-title {
    margin-bottom: 20rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  .history-list {
    .history-item {
      display: flex;
      justify-content: space-between;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #eee;
      &:last-child {
        border-bottom: none;
      }
      .history-date {
        font-size: 28rpx;
        color: #666;
      }
      .history-status {
        font-size: 28rpx;
        &.punched {
          color: #28a745;
        }
      }
    }
    .no-records {
      text-align: center;
      font-size: 32rpx;
      color: #666;
      padding: 40rpx;
    }
  }
}
</style>