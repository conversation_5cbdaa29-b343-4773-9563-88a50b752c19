<template>
  <view class="data-analysis">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">数据分析</text>
    </view>

    <!-- 统计数据 -->
    <view class="stats-container">
      <view class="stat-item">
        <text class="stat-label">总打卡天数</text>
        <text class="stat-value">{{ totalPunchDays }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">连续打卡天数</text>
        <text class="stat-value">{{ streakDays }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">完成计划数</text>
        <text class="stat-value">{{ completedPlans }}</text>
      </view>
    </view>

    <!-- 日历展示打卡情况 -->
    <view class="calendar-container">
      <view class="calendar-header">
        <button class="nav-button" @click="prevMonth">◄</button>
        <text class="calendar-title">{{ currentYear }}年{{ currentMonth + 1 }}月</text>
        <button class="nav-button" @click="nextMonth">►</button>
      </view>
      <view class="calendar-grid">
        <text class="day-name" v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day">{{ day }}</text>
        <text v-for="(day, index) in calendarDays" :key="index" class="day" :class="{ 'punched': day.punched }">
          {{ day.date ? day.date : '' }}
        </text>
      </view>
    </view>

    <!-- 完成率统计 -->
    <view class="rates-container">
      <view class="rate-item">
        <text class="rate-label">连续打卡率</text>
        <text class="rate-value">{{ streakRate }}%</text>
      </view>
      <view class="rate-item">
        <text class="rate-label">计划完成率</text>
        <text class="rate-value">{{ planCompletionRate }}%</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 统计数据
const totalPunchDays = ref(0);
const streakDays = ref(0);
const completedPlans = ref(0);
const totalPlans = ref(0); // 用于计算计划完成率

// 日历相关数据
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const punchRecords = ref([]);

// 加载并计算统计数据
import { apiRequest } from '@/utils/request.js';
const loadStats = async () => {
  try {
    // 后端计划
    const plans = await apiRequest({ url: '/api/plans', method: 'GET' });
    completedPlans.value = (plans || []).filter(plan => plan.completed).length;
    totalPlans.value = (plans || []).length;
    // 后端打卡
    const punches = await apiRequest({ url: '/api/punches', method: 'GET' });
    punchRecords.value = punches || [];
    totalPunchDays.value = punchRecords.value.filter(record => record.punched).length;
    streakDays.value = calculateStreakDays(punchRecords.value);
  } catch (e) {
    totalPunchDays.value = 0;
    streakDays.value = 0;
    completedPlans.value = 0;
    totalPlans.value = 0;
    punchRecords.value = [];
  }
};

// 计算连续打卡天数
const calculateStreakDays = (records) => {
  if (!records.length) return 0;

  const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
  let streak = 0;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  let currentDate = sortedRecords.some(r => r.date === formatDate(today) && r.punched)
    ? today
    : sortedRecords.some(r => r.date === formatDate(yesterday) && r.punched)
    ? yesterday
    : null;

  if (!currentDate) return 0;

  for (const record of sortedRecords) {
    const recordDate = new Date(record.date);
    if (record.punched && formatDate(recordDate) === formatDate(currentDate)) {
      streak++;
      currentDate.setDate(currentDate.getDate() - 1);
    } else if (recordDate < currentDate) {
      break;
    }
  }

  return streak;
};

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 计算当前月份的日历天数
const calendarDays = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startDay = firstDay.getDay();
  const days = [];

  for (let i = 0; i < startDay; i++) {
    days.push({ date: null, punched: false });
  }

  for (let i = 1; i <= daysInMonth; i++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
    const punched = punchRecords.value.some(record => record.date === dateStr && record.punched);
    days.push({ date: i, punched });
  }

  return days;
});

// 计算完成率
const streakRate = computed(() => {
  if (totalPunchDays.value === 0) return 0;
  return Math.round((streakDays.value / totalPunchDays.value) * 100);
});

const planCompletionRate = computed(() => {
  if (totalPlans.value === 0) return 0;
  return Math.round((completedPlans.value / totalPlans.value) * 100);
});

// 切换到上个月
const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }
};

// 切换到下个月
const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadStats();
});
</script>

<style lang="scss">
.data-analysis {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.stats-container {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .stat-item {
    flex: 1;
    text-align: center;
    .stat-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .stat-value {
      font-size: 40rpx;
      font-weight: bold;
      color: #007aff;
    }
  }
}

.calendar-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  .nav-button {
    width: 60rpx;
    height: 60rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 28rpx;
  }
  .calendar-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10rpx;
  text-align: center;
  .day-name {
    font-size: 28rpx;
    color: #666;
  }
  .day {
    font-size: 28rpx;
    color: #333;
    height: 60rpx;
    line-height: 60rpx;
    &.punched {
      background-color: #007aff;
      color: #fff;
      border-radius: 50%;
    }
  }
}

.rates-container {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .rate-item {
    flex: 1;
    text-align: center;
    .rate-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .rate-value {
      font-size: 40rpx;
      font-weight: bold;
      color: #007aff;
    }
  }
}
</style>