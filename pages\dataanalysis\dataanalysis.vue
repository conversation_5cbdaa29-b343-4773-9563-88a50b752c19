<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">数据分析</text>
      <text class="header-subtitle">深度分析学习数据，优化学习策略</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 统计数据卡片 -->
      <view class="modern-card stats-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">📈</text>
          </view>
          <text class="card-title">学习统计</text>
        </view>

        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{ totalPunchDays }}</text>
            <text class="stat-label">总打卡天数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value streak">{{ streakDays }}</text>
            <text class="stat-label">连续打卡天数</text>
          </view>
          <view class="stat-item">
            <text class="stat-value completed">{{ completedPlans }}</text>
            <text class="stat-label">完成计划数</text>
          </view>
        </view>
      </view>

      <!-- 日历展示卡片 -->
      <view class="modern-card calendar-card fade-in-up">
        <view class="calendar-header">
          <view class="calendar-nav">
            <button class="modern-button nav-button" @click="prevMonth">
              <text class="nav-icon">◄</text>
            </button>
            <text class="calendar-title">{{ currentYear }}年{{ currentMonth + 1 }}月</text>
            <button class="modern-button nav-button" @click="nextMonth">
              <text class="nav-icon">►</text>
            </button>
          </view>
        </view>

        <view class="calendar-grid">
          <text class="day-name" v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day">
            {{ day }}
          </text>
          <view
            v-for="(day, index) in calendarDays"
            :key="index"
            class="day-cell"
            :class="{ 'punched': day.punched, 'empty': !day.date }"
          >
            <text class="day-text">{{ day.date || '' }}</text>
          </view>
        </view>
      </view>

      <!-- 完成率统计卡片 -->
      <view class="modern-card rates-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">📊</text>
          </view>
          <text class="card-title">完成率分析</text>
        </view>

        <view class="rates-grid">
          <view class="rate-item">
            <view class="rate-circle">
              <text class="rate-value">{{ streakRate }}%</text>
            </view>
            <text class="rate-label">连续打卡率</text>
          </view>
          <view class="rate-item">
            <view class="rate-circle">
              <text class="rate-value">{{ planCompletionRate }}%</text>
            </view>
            <text class="rate-label">计划完成率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';

// 统计数据
const totalPunchDays = ref(0);
const streakDays = ref(0);
const completedPlans = ref(0);
const totalPlans = ref(0); // 用于计算计划完成率

// 日历相关数据
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth());
const punchRecords = ref([]);

// 加载并计算统计数据
import { apiRequest } from '@/utils/request.js';
const loadStats = async () => {
  try {
    // 后端计划
    const plans = await apiRequest({ url: '/api/plans', method: 'GET' });
    completedPlans.value = (plans || []).filter(plan => plan.completed).length;
    totalPlans.value = (plans || []).length;
    // 后端打卡
    const punches = await apiRequest({ url: '/api/punches', method: 'GET' });
    punchRecords.value = punches || [];
    totalPunchDays.value = punchRecords.value.filter(record => record.punched).length;
    streakDays.value = calculateStreakDays(punchRecords.value);
  } catch (e) {
    totalPunchDays.value = 0;
    streakDays.value = 0;
    completedPlans.value = 0;
    totalPlans.value = 0;
    punchRecords.value = [];
  }
};

// 计算连续打卡天数
const calculateStreakDays = (records) => {
  if (!records.length) return 0;

  const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
  let streak = 0;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  let currentDate = sortedRecords.some(r => r.date === formatDate(today) && r.punched)
    ? today
    : sortedRecords.some(r => r.date === formatDate(yesterday) && r.punched)
    ? yesterday
    : null;

  if (!currentDate) return 0;

  for (const record of sortedRecords) {
    const recordDate = new Date(record.date);
    if (record.punched && formatDate(recordDate) === formatDate(currentDate)) {
      streak++;
      currentDate.setDate(currentDate.getDate() - 1);
    } else if (recordDate < currentDate) {
      break;
    }
  }

  return streak;
};

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 计算当前月份的日历天数
const calendarDays = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startDay = firstDay.getDay();
  const days = [];

  for (let i = 0; i < startDay; i++) {
    days.push({ date: null, punched: false });
  }

  for (let i = 1; i <= daysInMonth; i++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
    const punched = punchRecords.value.some(record => record.date === dateStr && record.punched);
    days.push({ date: i, punched });
  }

  return days;
});

// 计算完成率
const streakRate = computed(() => {
  if (totalPunchDays.value === 0) return 0;
  return Math.round((streakDays.value / totalPunchDays.value) * 100);
});

const planCompletionRate = computed(() => {
  if (totalPlans.value === 0) return 0;
  return Math.round((completedPlans.value / totalPlans.value) * 100);
});

// 切换到上个月
const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }
};

// 切换到下个月
const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadStats();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 统计卡片样式
.stats-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--monitor-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;

    .stat-item {
      text-align: center;

      .stat-value {
        display: block;
        font-size: 36rpx;
        font-weight: 700;
        color: var(--monitor-gradient);
        margin-bottom: 8rpx;

        &.streak {
          color: var(--checkin-gradient);
        }

        &.completed {
          color: var(--achievement-gradient);
        }
      }

      .stat-label {
        font-size: 22rpx;
        color: var(--gray-500);
        font-weight: 500;
      }
    }
  }
}

// 日历卡片样式
.calendar-card {
  .calendar-header {
    margin-bottom: 24rpx;

    .calendar-nav {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .nav-button {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .nav-icon {
          font-size: 24rpx;
          color: white;
        }
      }

      .calendar-title {
        font-size: 28rpx;
        font-weight: 600;
        color: var(--gray-800);
      }
    }
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8rpx;

    .day-name {
      font-size: 22rpx;
      color: var(--gray-500);
      font-weight: 600;
      text-align: center;
      padding: 12rpx 0;
    }

    .day-cell {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s ease;

      &.empty {
        opacity: 0;
      }

      &.punched {
        background: var(--checkin-gradient);

        .day-text {
          color: white;
          font-weight: 600;
        }
      }

      .day-text {
        font-size: 24rpx;
        color: var(--gray-700);
      }
    }
  }
}

// 完成率卡片样式
.rates-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--plan-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .rates-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32rpx;

    .rate-item {
      text-align: center;

      .rate-circle {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--monitor-gradient), var(--achievement-gradient));
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16rpx;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          inset: 8rpx;
          border-radius: 50%;
          background: white;
        }

        .rate-value {
          font-size: 24rpx;
          font-weight: 700;
          color: var(--gray-800);
          position: relative;
          z-index: 1;
        }
      }

      .rate-label {
        font-size: 22rpx;
        color: var(--gray-500);
        font-weight: 500;
      }
    }
  }
}
</style>