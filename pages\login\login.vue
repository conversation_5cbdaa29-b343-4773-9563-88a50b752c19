<template>
  <view class="modern-page login-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">自律助手</text>
      <text class="header-subtitle">登录您的账户，开启学习之旅</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 登录卡片 -->
      <view class="modern-card login-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">🔑</text>
          </view>
          <text class="card-title">手机号登录/注册</text>
          <text class="card-subtitle">请输入您的手机号以登录/注册</text>
        </view>

        <view class="login-form">
          <!-- 手机号输入区域 -->
          <view class="form-group">
            <text class="form-label">手机号</text>
            <view class="phone-input-container">
              <button class="region-selector" @click="goToNationSelect">
                <text class="region-text">{{ displayRegion }}</text>
                <text class="selector-arrow">▼</text>
              </button>
              <input
                class="modern-input phone-input"
                v-model="identifier"
                placeholder="请输入手机号"
                type="number"
              />
            </view>
          </view>

          <!-- 密码输入区域 -->
          <view class="form-group">
            <text class="form-label">密码</text>
            <view class="password-input-container">
              <input
                class="modern-input password-input"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                placeholder="请输入密码"
              />
              <button class="password-toggle" @click="togglePassword">
                <text class="toggle-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
              </button>
            </view>
          </view>

          <!-- 登录按钮 -->
          <button class="modern-button primary login-btn" @click="handleLogin">
            <text class="btn-icon">🚀</text>
            <text class="btn-text">继续</text>
          </button>

          <!-- 忘记密码 -->
          <button class="forget-password-btn" @click="showResetPwd = true">
            忘记密码？
          </button>

          <!-- 条款声明 -->
          <view class="terms-section">
            <text class="terms-text">
              点击继续即表示您同意我们的
              <text class="terms-link" @click="showTerms">服务条款</text>
              和
              <text class="terms-link" @click="showPrivacy">隐私政策</text>
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>

    <!-- 忘记密码弹窗 -->
    <uni-popup v-model="showResetPwd" type="dialog">
      <view class="modern-modal reset-pwd-modal">
        <view class="modal-header">
          <text class="modal-title">重置密码</text>
        </view>
        <view class="modal-body">
          <input
            class="modern-input"
            v-model="resetPwd"
            placeholder="请输入新密码"
            type="password"
          />
        </view>
        <view class="modal-footer">
          <button class="modern-button secondary" @click="showResetPwd = false">取消</button>
          <button class="modern-button primary" @click="handleResetPwd">重置密码</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
const showResetPwd = ref(false);
const resetPwd = ref('');

// 登录输入数据
const identifier = ref('');
const password = ref('');
const showPassword = ref(false); // 控制密码显示/隐藏

// 默认地区（中国）
const selectedRegion = ref({
  name: '中国🇨🇳:+86',
  code: '+86'
});

// 计算属性：只显示国旗和区号，移除国名
const displayRegion = computed(() => {
  const region = selectedRegion.value.name;
  const parts = region.split(':');
  const flag = parts[0].replace(/[^🇨🇳🇺🇳🇺🇸🇬🇧🇯🇵🇰🇷🇩🇪🇫🇷🇮🇹🇪🇸🇷🇺🇮🇳🇧🇷🇦🇺🇨🇦🇲🇽🇿🇦🇸🇬🇹🇭🇻🇳🇵🇭🇲🇾🇮🇩🇹🇷🇸🇦🇦🇪🇪🇬🇳🇬🇰🇪🇬🇭🇦🇷🇨🇴🇵🇪🇨🇱🇻🇪🇪🇨🇧🇴🇵🇾🇺🇾🇨🇺🇩🇴🇭🇹🇬🇹🇭🇳🇳🇮🇨🇷🇵🇦🇸🇻🇯🇲🇹🇹🇧🇸🇧🇧🇬🇾🇸🇷🇧🇿🇰🇳🇱🇨🇻🇨🇬🇩🇦🇬🇩🇲🇰🇾🇧🇲🇻🇬🇹🇨🇦🇼🇨🇼🇸🇽🇧🇶🇬🇱🇫🇴🇮🇸🇳🇴🇸🇪🇫🇮🇩🇰]/g, ''); // 提取国旗
  const dialCode = parts[1] || selectedRegion.value.code; // 提取区号
  return `${flag}:${dialCode}`; // 返回国旗和区号
});

// 页面加载时初始化
onMounted(() => {
  // 加载存储的地区选择
  const storedRegion = uni.getStorageSync('selectedRegion');
  if (storedRegion) {
    selectedRegion.value = storedRegion;
  }

  // 监听从 nation.vue 返回的地区选择
  uni.$on('updateRegion', handleRegionUpdate);
});

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('updateRegion', handleRegionUpdate);
});

// 处理地区更新
const handleRegionUpdate = (region) => {
  selectedRegion.value = region;
  uni.setStorageSync('selectedRegion', region); // 持久化存储选择结果
};



// 跳转到地区选择页面
const goToNationSelect = () => {
  uni.navigateTo({
    url: `/pages/nation/nation?selectedRegion=${JSON.stringify(selectedRegion.value)}`
  });
};

// 保存登录状态
const saveLoginData = () => {
  const loginData = {
    isLoggedIn: true
  };
  uni.setStorageSync('loginData', loginData);
};

// 忘记密码-重置密码（调试用，直接改密码）
const handleResetPwd = () => {
  if (!identifier.value.trim() || !resetPwd.value.trim()) {
    uni.showToast({ title: '请输入手机号和新密码', icon: 'none' });
    return;
  }
  const username = `${selectedRegion.value.code}-${identifier.value}`;
  console.log('注册/登录/重置密码 username:', username);
  uni.request({
    url: 'http://localhost:5000/api/reset_pwd',
    method: 'POST',
    data: {
      username,
      new_password: resetPwd.value
    },
    success: (res) => {
      if (res.data.msg === '密码重置成功') {
        uni.showToast({ title: '重置成功', icon: 'success' });
        showResetPwd.value = false;
        resetPwd.value = '';
      } else {
        uni.showToast({ title: res.data.msg || '重置失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    }
  });
};

// 注册并自动登录（同一按钮，用户名为区号-手机号）
const handleLogin = () => {
  if (!identifier.value.trim() || !password.value.trim()) {
    uni.showToast({ title: '请输入手机号和密码', icon: 'none' });
    return;
  }
  const username = `${selectedRegion.value.code}-${identifier.value}`;
  // 先尝试注册
  uni.request({
    url: 'http://localhost:5000/api/register',
    method: 'POST',
    data: {
      username,
      password: password.value
    },
    success: (regRes) => {
      // 注册成功或已注册都去登录
      if (regRes.data.msg === '注册成功' || regRes.data.msg === '用户名已存在') {
        // 自动登录
        uni.request({
          url: 'http://localhost:5000/api/login',
          method: 'POST',
          data: {
            username,
            password: password.value
          },
          success: (res) => {
            if (res.data.token) {
              uni.setStorageSync('token', res.data.token);
              uni.setStorageSync('loginData', { isLoggedIn: true });
              // 获取用户信息
              uni.request({
                url: 'http://localhost:5000/api/me',
                method: 'GET',
                header: { Authorization: 'Bearer ' + res.data.token },
                success: (userRes) => {
                  if (userRes.data.username) {
                    uni.setStorageSync('userData', userRes.data);
                  }
                  uni.showToast({ title: '登录成功', icon: 'success' });
                  setTimeout(() => {
                    uni.reLaunch({ url: '/pages/enter/enter' });
                  }, 1000);
                },
                fail: () => {
                  uni.showToast({ title: '获取用户信息失败', icon: 'none' });
                }
              });
            } else {
              uni.showToast({ title: res.data.msg || '登录失败', icon: 'none' });
            }
          },
          fail: () => {
            uni.showToast({ title: '网络错误', icon: 'none' });
          }
        });
      } else {
        uni.showToast({ title: regRes.data.msg || '注册失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    }
  });
};



// 显示/隐藏密码
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// 显示服务条款弹窗
const showTerms = () => {
  uni.showModal({
    title: '服务条款',
    content: '欢迎使用自律助手！在使用本应用前，请仔细阅读以下条款：\n\n' +
             '1. 用户需遵守所有适用的法律法规，不得利用本应用从事任何非法活动。\n' +
             '2. 本应用提供的所有内容仅供参考，用户需自行承担使用后果。\n' +
             '3. 我们有权随时更新或修改服务条款，更新后将通过应用内通知用户。\n' +
             '4. 用户不得以任何方式干扰本应用的正常运行，包括但不限于传播病毒、恶意攻击等。\n' +
             '5. 如有任何争议，将按照中华人民共和国法律解决。\n\n' +
             '若您继续使用本应用，即表示您已同意上述条款。',
    showCancel: false,
    confirmText: '我已阅读'
  });
};

// 显示隐私政策弹窗
const showPrivacy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '我们非常重视您的隐私，以下是自律助手的隐私政策：\n\n' +
             '1. 我们可能会收集您的用户名、手机号、邮箱等信息，用于提供和优化服务。\n' +
             '2. 您的个人信息将受到严格保护，不会出售或未经授权分享给第三方。\n' +
             '3. 我们使用安全技术（如加密）保护您的数据，但无法完全保证数据绝对安全。\n' +
             '4. 您有权随时查看、修改或删除您的个人信息，具体操作可通过设置页面完成。\n' +
             '5. 本应用可能会使用第三方服务（如第三方登录），其隐私政策由相应服务提供商负责。\n\n' +
             '若您继续使用本应用，即表示您已同意上述隐私政策。',
    showCancel: false,
    confirmText: '我已阅读'
  });
};
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 登录页面特殊样式
.login-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

// 登录卡片样式
.login-card {
  max-width: 600rpx;
  width: 100%;

  .card-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 32rpx;

    .header-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: var(--plan-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16rpx;

      .icon-text {
        font-size: 32rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--gray-800);
      margin-bottom: 8rpx;
    }

    .card-subtitle {
      font-size: 24rpx;
      color: var(--gray-500);
      text-align: center;
    }
  }

  .login-form {
    .form-group {
      margin-bottom: 24rpx;

      .form-label {
        display: block;
        font-size: 24rpx;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 8rpx;
      }

      .phone-input-container {
        display: flex;

        .region-selector {
          width: 140rpx;
          height: 80rpx;
          border: 2rpx solid var(--gray-200);
          border-right: none;
          border-radius: var(--radius-lg) 0 0 var(--radius-lg);
          background: var(--white);
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 12rpx;
          transition: all 0.3s ease;

          &:active {
            border-color: var(--plan-gradient);
          }

          .region-text {
            font-size: 24rpx;
            color: var(--gray-800);
            flex: 1;
            text-align: center;
          }

          .selector-arrow {
            font-size: 16rpx;
            color: var(--gray-500);
          }
        }

        .phone-input {
          flex: 1;
          border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
          border-left: none;
        }
      }

      .password-input-container {
        position: relative;

        .password-input {
          width: 100%;
          padding-right: 80rpx;
        }

        .password-toggle {
          position: absolute;
          right: 20rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 50rpx;
          height: 50rpx;
          border: none;
          background: transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.3s ease;

          &:active {
            background: var(--gray-100);
          }

          .toggle-icon {
            font-size: 24rpx;
            color: var(--gray-500);
          }
        }
      }

      .modern-input {
        width: 100%;
        height: 80rpx;
        border: 2rpx solid var(--gray-200);
        border-radius: var(--radius-lg);
        padding: 0 24rpx;
        font-size: 28rpx;
        color: var(--gray-800);
        background: var(--white);
        transition: all 0.3s ease;

        &:focus {
          border-color: var(--plan-gradient);
          box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
        }

        &::placeholder {
          color: var(--gray-400);
        }
      }
    }

    .login-btn {
      width: 100%;
      height: 80rpx;
      margin: 32rpx 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;

      .btn-icon {
        font-size: 20rpx;
      }

      .btn-text {
        font-size: 28rpx;
        font-weight: 500;
      }
    }

    .forget-password-btn {
      width: 100%;
      height: 60rpx;
      background: transparent;
      border: none;
      color: var(--plan-gradient);
      font-size: 24rpx;
      margin-bottom: 24rpx;
      transition: all 0.3s ease;

      &:active {
        opacity: 0.7;
      }
    }

    .terms-section {
      text-align: center;

      .terms-text {
        font-size: 22rpx;
        color: var(--gray-500);
        line-height: 1.4;

        .terms-link {
          color: var(--plan-gradient);
          text-decoration: underline;
        }
      }
    }
  }
}

// 重置密码模态框样式
.reset-pwd-modal {
  width: 500rpx;
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;

  .modal-header {
    padding: 32rpx 32rpx 24rpx;
    text-align: center;
    border-bottom: 1rpx solid var(--gray-200);

    .modal-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .modal-body {
    padding: 24rpx 32rpx;

    .modern-input {
      width: 100%;
      height: 80rpx;
      border: 2rpx solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: 0 24rpx;
      font-size: 26rpx;
      color: var(--gray-800);
      background: var(--white);
      transition: all 0.3s ease;

      &:focus {
        border-color: var(--plan-gradient);
        box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
      }

      &::placeholder {
        color: var(--gray-400);
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 16rpx;
    padding: 24rpx 32rpx 32rpx;

    .modern-button {
      flex: 1;
      height: 70rpx;
      font-size: 26rpx;
    }
  }
}
</style>