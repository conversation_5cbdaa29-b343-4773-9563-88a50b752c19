<template>
  <view class="login-page">
    <!-- 标题 -->
    <view class="header">
      <text class="app-name">自律助手</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-container">
      <view class="login-form">
        <text class="form-title">手机号登录/注册</text>
        <text class="form-subtitle">请输入您的手机号以登录/注册</text>
        <!-- 手机号输入框 -->
        <view class="input-container">
          <button class="region-button" @click="goToNationSelect">
            <text class="region-display">{{ displayRegion }}</text>
          </button>
          <input
            class="input-field phone-input"
            v-model="identifier"
            placeholder="手机号"
            type="number"
          />
        </view>
        <!-- 密码输入框，添加显示/隐藏密码功能 -->
        <view class="password-container">
          <input
            class="input-field password-input"
            v-model="password"
            :type="showPassword ? 'text' : 'password'"
            placeholder="密码"
          />
          <view class="toggle-password" @click="togglePassword">
            <image
              class="password-icon"
              :src="showPassword ? '/common/images/icon/eye-open.png' : '/common/images/icon/eye-closed.png'"
              mode="aspectFit"
            />
          </view>
        </view>

        <!-- 继续按钮 -->
        <button class="continue-button" @click="handleLogin">继续</button>



        <!-- 条款声明 -->
        <text class="terms-text">
          点击继续即表示您同意我们的
          <text class="terms-link" @click="showTerms">服务条款</text>
          和
          <text class="terms-link" @click="showPrivacy">隐私政策</text>
        </text>
      </view>
    </view>
  </view>
  <!-- 忘记密码按钮 -->
  <button class="forget-password-button" @click="showResetPwd = true">忘记密码？</button>

  <!-- 忘记密码弹窗 -->
  <uni-popup v-model="showResetPwd" type="dialog">
    <view class="reset-pwd-dialog">
      <input class="input-field" v-model="resetPwd" placeholder="请输入新密码" type="text" />
      <button class="reset-pwd-btn" @click="handleResetPwd">重置密码</button>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
const showResetPwd = ref(false);
const resetPwd = ref('');

// 登录输入数据
const identifier = ref('');
const password = ref('');
const showPassword = ref(false); // 控制密码显示/隐藏

// 默认地区（中国）
const selectedRegion = ref({
  name: '中国🇨🇳:+86',
  code: '+86'
});

// 计算属性：只显示国旗和区号，移除国名
const displayRegion = computed(() => {
  const region = selectedRegion.value.name;
  const parts = region.split(':');
  const flag = parts[0].replace(/[^🇨🇳🇺🇳🇺🇸🇬🇧🇯🇵🇰🇷🇩🇪🇫🇷🇮🇹🇪🇸🇷🇺🇮🇳🇧🇷🇦🇺🇨🇦🇲🇽🇿🇦🇸🇬🇹🇭🇻🇳🇵🇭🇲🇾🇮🇩🇹🇷🇸🇦🇦🇪🇪🇬🇳🇬🇰🇪🇬🇭🇦🇷🇨🇴🇵🇪🇨🇱🇻🇪🇪🇨🇧🇴🇵🇾🇺🇾🇨🇺🇩🇴🇭🇹🇬🇹🇭🇳🇳🇮🇨🇷🇵🇦🇸🇻🇯🇲🇹🇹🇧🇸🇧🇧🇬🇾🇸🇷🇧🇿🇰🇳🇱🇨🇻🇨🇬🇩🇦🇬🇩🇲🇰🇾🇧🇲🇻🇬🇹🇨🇦🇼🇨🇼🇸🇽🇧🇶🇬🇱🇫🇴🇮🇸🇳🇴🇸🇪🇫🇮🇩🇰]/g, ''); // 提取国旗
  const dialCode = parts[1] || selectedRegion.value.code; // 提取区号
  return `${flag}:${dialCode}`; // 返回国旗和区号
});

// 页面加载时初始化
onMounted(() => {
  // 加载存储的地区选择
  const storedRegion = uni.getStorageSync('selectedRegion');
  if (storedRegion) {
    selectedRegion.value = storedRegion;
  }

  // 监听从 nation.vue 返回的地区选择
  uni.$on('updateRegion', handleRegionUpdate);
});

// 页面卸载时移除事件监听
onUnmounted(() => {
  uni.$off('updateRegion', handleRegionUpdate);
});

// 处理地区更新
const handleRegionUpdate = (region) => {
  selectedRegion.value = region;
  uni.setStorageSync('selectedRegion', region); // 持久化存储选择结果
};



// 跳转到地区选择页面
const goToNationSelect = () => {
  uni.navigateTo({
    url: `/pages/nation/nation?selectedRegion=${JSON.stringify(selectedRegion.value)}`
  });
};

// 保存登录状态
const saveLoginData = () => {
  const loginData = {
    isLoggedIn: true
  };
  uni.setStorageSync('loginData', loginData);
};

// 忘记密码-重置密码（调试用，直接改密码）
const handleResetPwd = () => {
  if (!identifier.value.trim() || !resetPwd.value.trim()) {
    uni.showToast({ title: '请输入手机号和新密码', icon: 'none' });
    return;
  }
  const username = `${selectedRegion.value.code}-${identifier.value}`;
  console.log('注册/登录/重置密码 username:', username);
  uni.request({
    url: 'http://localhost:5000/api/reset_pwd',
    method: 'POST',
    data: {
      username,
      new_password: resetPwd.value
    },
    success: (res) => {
      if (res.data.msg === '密码重置成功') {
        uni.showToast({ title: '重置成功', icon: 'success' });
        showResetPwd.value = false;
        resetPwd.value = '';
      } else {
        uni.showToast({ title: res.data.msg || '重置失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    }
  });
};

// 注册并自动登录（同一按钮，用户名为区号-手机号）
const handleLogin = () => {
  if (!identifier.value.trim() || !password.value.trim()) {
    uni.showToast({ title: '请输入手机号和密码', icon: 'none' });
    return;
  }
  const username = `${selectedRegion.value.code}-${identifier.value}`;
  // 先尝试注册
  uni.request({
    url: 'http://localhost:5000/api/register',
    method: 'POST',
    data: {
      username,
      password: password.value
    },
    success: (regRes) => {
      // 注册成功或已注册都去登录
      if (regRes.data.msg === '注册成功' || regRes.data.msg === '用户名已存在') {
        // 自动登录
        uni.request({
          url: 'http://localhost:5000/api/login',
          method: 'POST',
          data: {
            username,
            password: password.value
          },
          success: (res) => {
            if (res.data.token) {
              uni.setStorageSync('token', res.data.token);
              uni.setStorageSync('loginData', { isLoggedIn: true });
              // 获取用户信息
              uni.request({
                url: 'http://localhost:5000/api/me',
                method: 'GET',
                header: { Authorization: 'Bearer ' + res.data.token },
                success: (userRes) => {
                  if (userRes.data.username) {
                    uni.setStorageSync('userData', userRes.data);
                  }
                  uni.showToast({ title: '登录成功', icon: 'success' });
                  setTimeout(() => {
                    uni.reLaunch({ url: '/pages/enter/enter' });
                  }, 1000);
                },
                fail: () => {
                  uni.showToast({ title: '获取用户信息失败', icon: 'none' });
                }
              });
            } else {
              uni.showToast({ title: res.data.msg || '登录失败', icon: 'none' });
            }
          },
          fail: () => {
            uni.showToast({ title: '网络错误', icon: 'none' });
          }
        });
      } else {
        uni.showToast({ title: regRes.data.msg || '注册失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    }
  });
};



// 显示/隐藏密码
const togglePassword = () => {
  showPassword.value = !showPassword.value;
};

// 显示服务条款弹窗
const showTerms = () => {
  uni.showModal({
    title: '服务条款',
    content: '欢迎使用自律助手！在使用本应用前，请仔细阅读以下条款：\n\n' +
             '1. 用户需遵守所有适用的法律法规，不得利用本应用从事任何非法活动。\n' +
             '2. 本应用提供的所有内容仅供参考，用户需自行承担使用后果。\n' +
             '3. 我们有权随时更新或修改服务条款，更新后将通过应用内通知用户。\n' +
             '4. 用户不得以任何方式干扰本应用的正常运行，包括但不限于传播病毒、恶意攻击等。\n' +
             '5. 如有任何争议，将按照中华人民共和国法律解决。\n\n' +
             '若您继续使用本应用，即表示您已同意上述条款。',
    showCancel: false,
    confirmText: '我已阅读'
  });
};

// 显示隐私政策弹窗
const showPrivacy = () => {
  uni.showModal({
    title: '隐私政策',
    content: '我们非常重视您的隐私，以下是自律助手的隐私政策：\n\n' +
             '1. 我们可能会收集您的用户名、手机号、邮箱等信息，用于提供和优化服务。\n' +
             '2. 您的个人信息将受到严格保护，不会出售或未经授权分享给第三方。\n' +
             '3. 我们使用安全技术（如加密）保护您的数据，但无法完全保证数据绝对安全。\n' +
             '4. 您有权随时查看、修改或删除您的个人信息，具体操作可通过设置页面完成。\n' +
             '5. 本应用可能会使用第三方服务（如第三方登录），其隐私政策由相应服务提供商负责。\n\n' +
             '若您继续使用本应用，即表示您已同意上述隐私政策。',
    showCancel: false,
    confirmText: '我已阅读'
  });
};
</script>

<style lang="scss">
.login-page {
  padding: 60rpx 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header {
  margin-bottom: 40rpx;
  .app-name {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
  }
}

.login-container {
  width: 100%;
  max-width: 600rpx;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.login-form {
  .form-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 10rpx;
  }
  .form-subtitle {
    font-size: 28rpx;
    color: #666;
    display: block;
    margin-bottom: 30rpx;
  }
  .login-type-switch {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    .type-button {
      width: 48%;
      height: 80rpx;
      background-color: #fff;
      border: 2rpx solid #ddd;
      border-radius: 10rpx;
      font-size: 28rpx;
      color: #333;
      &.active {
        border-color: #007aff;
        color: #007aff;
      }
      &:active {
        background-color: #f0f8ff;
      }
    }
  }
  .input-container {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    .region-button {
      width: 120rpx; /* 调整宽度以适应国旗和区号 */
      height: 90rpx;
      border: 2rpx solid #ddd;
      border-right: none;
      border-radius: 10rpx 0 0 10rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      padding: 0 10rpx;
      .region-display {
        font-size: 28rpx;
        color: #333;
        text-align: center;
        white-space: nowrap; /* 防止换行 */
      }
    }
    .phone-input {
      flex: 1;
      height: 90rpx;
      border: 2rpx solid #ddd;
      border-radius: 0 10rpx 10rpx 0;
      padding: 0 20rpx;
      font-size: 28rpx;
    }
  }
  .input-field {
    width: 100%;
    height: 90rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    box-sizing: border-box; /* 确保边框和内边距不影响宽度 */
  }
  .password-container {
    position: relative;
    margin-bottom: 20rpx;
    .password-input {
      width: 100%;
      height: 90rpx;
      border: 2rpx solid #ddd;
      border-radius: 10rpx;
      padding: 0 80rpx 0 20rpx; /* 为眼睛图标留出空间 */
      font-size: 28rpx;
      background-color: #fff;
      box-sizing: border-box;
    }
    .toggle-password {
      position: absolute;
      right: 20rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .password-icon {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
  .continue-button {
    width: 100%;
    height: 90rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 20rpx;
    font-size: 32rpx;
    margin: 30rpx 0;
    &:active {
      background-color: #005bb5;
    }
  }
}

.third-party-login {
  .divider {
    display: flex;
    align-items: center;
    margin: 20rpx 0;
    .divider-line {
      flex: 1;
      height: 2rpx;
      background-color: #ddd;
    }
    .divider-text {
      margin: 0 20rpx;
      font-size: 28rpx;
      color: #666;
    }
  }
  .third-party-button {
    width: 100%;
    height: 90rpx;
    background-color: #fff;
    border: 2rpx solid #007aff;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;
    &:active {
      background-color: #f0f8ff;
    }
    .third-party-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
    }
    text {
      font-size: 28rpx;
      color: #333;
    }
  }
}

.terms-text {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 30rpx;
  .terms-link {
    color: #007aff;
    text-decoration: underline;
  }
}
</style>