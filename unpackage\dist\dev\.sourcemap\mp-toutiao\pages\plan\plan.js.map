{"version": 3, "file": "plan.js", "sources": ["pages/plan/plan.vue", "../../../Downloads/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGxhbi9wbGFuLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view>\n\t\t\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t}\n\t}\n</script>\n\n<style>\n\n</style>\n", "import MiniProgramPage from 'C:/Users/<USER>/Documents/HBuilderProjects/自律助手/pages/plan/plan.vue'\ntt.createPage(MiniProgramPage)"], "names": [], "mappings": ";;AAOC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,SAAS,CAET;AACD;;;;;ACfD,GAAG,WAAW,eAAe;"}