<template>
  <view class="achievement-wall">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">成就墙</text>
    </view>

    <!-- 成就统计 -->
    <view class="stats-container">
      <view class="stat-item">
        <text class="stat-label">已解锁成就</text>
        <text class="stat-value">{{ unlockedAchievements }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">总成就数</text>
        <text class="stat-value">{{ totalAchievements }}</text>
      </view>
    </view>

    <!-- 分类：累计打卡成就 -->
    <view class="category-section">
      <text class="category-title">累计打卡成就</text>
      <scroll-view class="achievement-list" scroll-x enable-flex>
        <view class="achievement-item" v-for="(achievement, index) in cumulativePunchAchievements" :key="index">
          <view class="achievement-icon" :class="{ 'locked': !achievement.unlocked }">
            <text>{{ achievement.icon }}</text>
          </view>
          <text class="achievement-title">{{ achievement.title }}</text>
          <text class="achievement-desc">{{ achievement.description }}</text>
          <text class="achievement-status" :class="{ 'unlocked': achievement.unlocked }">
            {{ achievement.unlocked ? '已解锁' : '未解锁' }}
          </text>
        </view>
        <view class="no-achievements" v-if="cumulativePunchAchievements.length === 0">
          <text>暂无成就，快去努力吧！</text>
        </view>
      </scroll-view>
    </view>

    <!-- 分类：连续打卡成就 -->
    <view class="category-section">
      <text class="category-title">连续打卡成就</text>
      <scroll-view class="achievement-list" scroll-x enable-flex>
        <view class="achievement-item" v-for="(achievement, index) in streakPunchAchievements" :key="index">
          <view class="achievement-icon" :class="{ 'locked': !achievement.unlocked }">
            <text>{{ achievement.icon }}</text>
          </view>
          <text class="achievement-title">{{ achievement.title }}</text>
          <text class="achievement-desc">{{ achievement.description }}</text>
          <text class="achievement-status" :class="{ 'unlocked': achievement.unlocked }">
            {{ achievement.unlocked ? '已解锁' : '未解锁' }}
          </text>
        </view>
        <view class="no-achievements" v-if="streakPunchAchievements.length === 0">
          <text>暂无成就，快去努力吧！</text>
        </view>
      </scroll-view>
    </view>

    <!-- 分类：专注成就 -->
    <view class="category-section">
      <text class="category-title">专注成就</text>
      <scroll-view class="achievement-list" scroll-x enable-flex>
        <view class="achievement-item" v-for="(achievement, index) in focusAchievements" :key="index">
          <view class="achievement-icon" :class="{ 'locked': !achievement.unlocked }">
            <text>{{ achievement.icon }}</text>
          </view>
          <text class="achievement-title">{{ achievement.title }}</text>
          <text class="achievement-desc">{{ achievement.description }}</text>
          <text class="achievement-status" :class="{ 'unlocked': achievement.unlocked }">
            {{ achievement.unlocked ? '已解锁' : '未解锁' }}
          </text>
        </view>
        <view class="no-achievements" v-if="focusAchievements.length === 0">
          <text>暂无成就，快去努力吧！</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 成就数据（按分类存储）
const cumulativePunchAchievements = ref([]);
const streakPunchAchievements = ref([]);
const focusAchievements = ref([]);

// 加载并初始化成就数据
const loadData = () => {
  try {
    // 加载已有成就状态
    const storedAchievements = uni.getStorageSync('achievements') || '{}';
    const savedAchievements = JSON.parse(storedAchievements);

    // 加载 Monitor.vue 和 Dailypunch.vue 数据
    const storedMonitorRecords = uni.getStorageSync('monitorRecords') || '[]';
    const monitorRecords = JSON.parse(storedMonitorRecords);
    const storedPunchRecords = uni.getStorageSync('punchRecords') || '[]';
    const punchRecords = JSON.parse(storedPunchRecords);

    // 计算统计数据
    const monitorData = calculateMonitorData(monitorRecords);
    const punchData = calculatePunchData(punchRecords);

    // 定义成就（按分类）
    const achievementDefinitions = {
      cumulativePunch: [
        { title: '打卡初体验', icon: '🎉', description: '累计打卡1天', condition: punchData.totalPunchDays >= 1 },
        { title: '打卡入门', icon: '🏅', description: '累计打卡5天', condition: punchData.totalPunchDays >= 5 },
        { title: '打卡小成', icon: '🌟', description: '累计打卡10天', condition: punchData.totalPunchDays >= 10 },
        { title: '打卡达人', icon: '✅', description: '累计打卡30天', condition: punchData.totalPunchDays >= 30 },
        { title: '打卡大师', icon: '👑', description: '累计打卡100天', condition: punchData.totalPunchDays >= 100 },
      ],
      streakPunch: [
        { title: '坚持第一步', icon: '🚀', description: '连续打卡3天', condition: punchData.streakDays >= 3 },
        { title: '习惯养成', icon: '🌱', description: '连续打卡7天', condition: punchData.streakDays >= 7 },
        { title: '持之以恒', icon: '💪', description: '连续打卡14天', condition: punchData.streakDays >= 14 },
        { title: '不懈努力', icon: '🔥', description: '连续打卡30天', condition: punchData.streakDays >= 30 },
        { title: '毅力王者', icon: '🏆', description: '连续打卡60天', condition: punchData.streakDays >= 60 },
      ],
      focus: [
        { title: '专注新手', icon: '🕒', description: '累计专注30分钟', condition: monitorData.totalStudyTime >= 30 },
        { title: '专注能手', icon: '🎯', description: '累计专注2小时', condition: monitorData.totalStudyTime >= 120 },
        { title: '专注大师', icon: '✨', description: '累计专注5小时', condition: monitorData.totalStudyTime >= 300 },
        { title: '专注王者', icon: '🌟', description: '累计专注10小时', condition: monitorData.totalStudyTime >= 600 },
        { title: '专注大王', icon: '🛸', description: '累计专注15小时', condition: monitorData.totalStudyTime >= 900 },
      ]
    };

    // 初始化成就状态
    cumulativePunchAchievements.value = achievementDefinitions.cumulativePunch.map(achievement => ({
      ...achievement,
      unlocked: savedAchievements[achievement.title] || achievement.condition
    }));
    streakPunchAchievements.value = achievementDefinitions.streakPunch.map(achievement => ({
      ...achievement,
      unlocked: savedAchievements[achievement.title] || achievement.condition
    }));
    focusAchievements.value = achievementDefinitions.focus.map(achievement => ({
      ...achievement,
      unlocked: savedAchievements[achievement.title] || achievement.condition
    }));

    // 保存更新后的成就状态
    saveData();
  } catch (e) {
    console.error('加载成就数据失败', e);
    cumulativePunchAchievements.value = [];
    streakPunchAchievements.value = [];
    focusAchievements.value = [];
  }
};

// 保存成就数据
const saveData = () => {
  const achievementsState = {};
  cumulativePunchAchievements.value.forEach(achievement => {
    achievementsState[achievement.title] = achievement.unlocked;
  });
  streakPunchAchievements.value.forEach(achievement => {
    achievementsState[achievement.title] = achievement.unlocked;
  });
  focusAchievements.value.forEach(achievement => {
    achievementsState[achievement.title] = achievement.unlocked;
  });
  uni.setStorageSync('achievements', JSON.stringify(achievementsState));
};

// 计算 Monitor.vue 数据
const calculateMonitorData = (records) => {
  let totalStudyTime = 0;

  records.forEach(record => {
    const timeMatch = record.detail.match(/监测时长: (\d{2}):(\d{2}):(\d{2})/);
    if (timeMatch) {
      const hours = parseInt(timeMatch[1]);
      const minutes = parseInt(timeMatch[2]);
      const seconds = parseInt(timeMatch[3]);
      totalStudyTime += (hours * 60) + minutes + (seconds / 60); // 转换为分钟
    }
  });

  return { totalStudyTime: Math.round(totalStudyTime) };
};

// 计算 Dailypunch.vue 数据
const calculatePunchData = (records) => {
  const totalPunchDays = records.filter(record => record.punched).length;
  const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
  let streakDays = 0;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  let currentDate = sortedRecords.some(r => r.date === formatDate(today) && r.punched)
    ? today
    : sortedRecords.some(r => r.date === formatDate(yesterday) && r.punched)
    ? yesterday
    : null;

  if (currentDate) {
    for (const record of sortedRecords) {
      const recordDate = new Date(record.date);
      if (record.punched && formatDate(recordDate) === formatDate(currentDate)) {
        streakDays++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (recordDate < currentDate) {
        break;
      }
    }
  }

  return { totalPunchDays, streakDays };
};

// 格式化日期
const formatDate = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 统计数据
const unlockedAchievements = computed(() => {
  return (
    cumulativePunchAchievements.value.filter(a => a.unlocked).length +
    streakPunchAchievements.value.filter(a => a.unlocked).length +
    focusAchievements.value.filter(a => a.unlocked).length
  );
});
const totalAchievements = computed(() => {
  return (
    cumulativePunchAchievements.value.length +
    streakPunchAchievements.value.length +
    focusAchievements.value.length
  );
});

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
.achievement-wall {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.stats-container {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .stat-item {
    flex: 1;
    text-align: center;
    .stat-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .stat-value {
      font-size: 40rpx;
      font-weight: bold;
      color: #007aff;
    }
  }
}

.category-section {
  margin-bottom: 40rpx;
  .category-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
}

.achievement-list {
  display: flex;
  padding: 20rpx 0;
  white-space: nowrap;
  .achievement-item {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    margin-right: 30rpx;
    width: 160rpx;
    .achievement-icon {
      width: 120rpx;
      height: 120rpx;
      border-radius: 50%;
      background-color: #007aff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 48rpx;
      margin-bottom: 10rpx;
      &.locked {
        background-color: #ccc;
      }
    }
    .achievement-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      text-align: center;
      margin-bottom: 10rpx;
    }
    .achievement-desc {
      font-size: 24rpx;
      color: #666;
      text-align: center;
      margin-bottom: 10rpx;
    }
    .achievement-status {
      font-size: 22rpx;
      text-align: center;
      &.unlocked {
        color: #28a745;
      }
      &:not(.unlocked) {
        color: #999;
      }
    }
  }
  .no-achievements {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
    width: 100%;
  }
}
</style>