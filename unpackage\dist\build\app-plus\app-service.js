if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";const t={__name:"index",setup:t=>(t,n)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"banner"},[e.createElementVNode("swiper",{circular:"true","indicator-dots":"","indicator-color":"rgba(255,255,255,0.5)","indicator-active-color":"#fff",autoplay:""},[e.createElementVNode("swiper-item",null,[e.createElementVNode("image",{src:"/assets/1.bcb9d2bf.png",mode:"aspectFill"})]),e.createElementVNode("swiper-item",null,[e.createElementVNode("image",{src:"/assets/2.c8a4c7ba.png",mode:"aspectFill"})]),e.createElementVNode("swiper-item",null,[e.createElementVNode("image",{src:"/assets/3.302e224c.png",mode:"aspectFill"})])])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/plan/plan"},[e.createElementVNode("button",{class:"custom-button"},"计划")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/daily/daily"},[e.createElementVNode("button",{class:"custom-button"},"每日打卡")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/monitor/monitor"},[e.createElementVNode("button",{class:"custom-button"},"学习监测")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/achievement/achievement"},[e.createElementVNode("button",{class:"custom-button"},"成就")])])],64))},n={__name:"society",setup:t=>(t,n)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/rankinglist/rankinglist"},[e.createElementVNode("button",{class:"custom-button"},"排行榜")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/friends/friends"},[e.createElementVNode("button",{class:"custom-button"},"好友")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/social/social"},[e.createElementVNode("button",{class:"custom-button"},"社交")])])],64))},a={__name:"achievement",setup:t=>(t,n)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/evaluate/evaluate"},[e.createElementVNode("button",{class:"custom-button"},"评价")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/achievementwall/achievementwall"},[e.createElementVNode("button",{class:"custom-button"},"成就墙")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/saying/saying"},[e.createElementVNode("button",{class:"custom-button"},"激励语录")])])],64))},o=(e,t)=>{const n=e.__vccOpts||e;for(const[a,o]of t)n[a]=o;return n};const r=o({},[["render",function(t,n){return e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",null,"登录"),e.createElementVNode("view",null,"用户名"),e.createElementVNode("input",{type:"text",placeholder:"请输入用户名"}),e.createElementVNode("view",null,"密码"),e.createElementVNode("input",{type:"text",placeholder:"请输入密码",password:""}),e.createElementVNode("button",{type:"primary",size:"mini"},"登录"),e.createElementVNode("view",null,[e.createElementVNode("h5",null,"新用户？")]),e.createElementVNode("button",{type:"primary",size:"mini"},"注册")],64)}]]),l={__name:"plan",setup:t=>(t,n)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/planmanagement/planmanagement"},[e.createElementVNode("button",{class:"custom-button"},"计划管理")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/timereminder/timereminder"},[e.createElementVNode("button",{class:"custom-button"},"时间提醒")])])],64))},c={__name:"daily",setup:t=>(t,n)=>(e.openBlock(),e.createElementBlock(e.Fragment,null,[e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/dailypunch/dailypunch"},[e.createElementVNode("button",{class:"custom-button"},"每日打卡")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/schedule/schedule"},[e.createElementVNode("button",{class:"custom-button"},"进度追踪")])]),e.createElementVNode("view",{class:"button-container"},[e.createElementVNode("navigator",{url:"/pages/dataanalysis/dataanalysis"},[e.createElementVNode("button",{class:"custom-button"},"数据分析")])])],64))};const i=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const s=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const u=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const m=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const d=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const p=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const g=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const E=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const v=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const V=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const N=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);const _=o({data:()=>({}),methods:{}},[["render",function(t,n,a,o,r,l){return e.openBlock(),e.createElementBlock("view")}]]);function y(e,t,...n){uni.__log__?uni.__log__(e,t,...n):console[e].apply(console,[...n,t])}__definePage("pages/index/index",t),__definePage("pages/society/society",n),__definePage("pages/achievement/achievement",a),__definePage("pages/enter/enter",r),__definePage("pages/plan/plan",l),__definePage("pages/daily/daily",c),__definePage("pages/monitor/monitor",i),__definePage("pages/planmanagement/planmanagement",s),__definePage("pages/timereminder/timereminder",u),__definePage("pages/dailypunch/dailypunch",m),__definePage("pages/schedule/schedule",d),__definePage("pages/dataanalysis/dataanalysis",p),__definePage("pages/evaluate/evaluate",g),__definePage("pages/achievementwall/achievementwall",E),__definePage("pages/saying/saying",v),__definePage("pages/rankinglist/rankinglist",V),__definePage("pages/friends/friends",N),__definePage("pages/social/social",_);const f={onLaunch:function(){y("log","at App.vue:4","App Launch")},onShow:function(){y("log","at App.vue:7","App Show")},onHide:function(){y("log","at App.vue:10","App Hide")}};const{app:b,Vuex:w,Pinia:h}={app:e.createVueApp(f)};uni.Vuex=w,uni.Pinia=h,b.provide("__globalStyles",__uniConfig.styles),b._component.mpType="app",b._component.render=()=>{},b.mount("#app")}(Vue);
