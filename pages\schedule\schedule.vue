<template>
  <view class="schedule">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">进度追踪</text>
    </view>

    <!-- 进度概览 -->
    <view class="progress-overview">
      <view class="overview-item">
        <text class="overview-label">总任务数</text>
        <text class="overview-value">{{ totalTasks }}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">已完成</text>
        <text class="overview-value">{{ completedTasks }}</text>
      </view>
      <view class="overview-item">
        <text class="overview-label">完成率</text>
        <text class="overview-value">{{ completionRate }}%</text>
      </view>
    </view>

    <!-- 任务进度列表 -->
    <view class="task-list">
      <view class="task-item" v-for="(task, index) in tasks" :key="index">
        <view class="task-info">
          <text class="task-title">{{ task.title }}</text>
          <text class="task-progress">进度: {{ task.progress }}%</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: task.progress + '%' }"></view>
        </view>
      </view>
      <view class="no-tasks" v-if="tasks.length === 0">
        <text>暂无任务</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 任务列表
const tasks = ref([]);

import { apiRequest } from '@/utils/request.js';
// 加载任务数据（全部后端）
const loadTasks = async () => {
  try {
    // 获取计划
    const planRes = await apiRequest({ url: '/api/plans', method: 'GET' });
    const plans = (planRes || []).map(plan => ({
      title: plan.title,
      progress: plan.completed ? 100 : 0,
    }));
    // 获取提醒
    let reminders = [];
    try {
      const reminderRes = await apiRequest({ url: '/api/reminders', method: 'GET' });
      reminders = (reminderRes || []).map(reminder => {
        const reminderTime = new Date(reminder.time);
        const now = new Date();
        const progress = reminderTime < now ? 0 : 50;
        return { title: reminder.title, progress };
      });
    } catch {}
    tasks.value = [...plans, ...reminders];
  } catch (e) {
    tasks.value = [];
  }
};

// 保存任务数据（这里仅为演示，实际由其他组件保存）
const saveTasks = () => {
  // 不直接保存 tasks，因为它是从 plans 和 reminders 派生的
  // 如果需要独立存储任务，可以在这里实现
};

// 计算统计数据
const totalTasks = computed(() => tasks.value.length);
const completedTasks = computed(() => tasks.value.filter(task => task.progress === 100).length);
const completionRate = computed(() => {
  if (totalTasks.value === 0) return 0;
  const totalProgress = tasks.value.reduce((sum, task) => sum + task.progress, 0);
  return Math.round(totalProgress / totalTasks.value);
});

// 组件挂载时加载数据
onMounted(() => {
  loadTasks();
});
</script>

<style lang="scss">
.schedule {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.progress-overview {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .overview-item {
    flex: 1;
    text-align: center;
    .overview-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .overview-value {
      font-size: 40rpx;
      font-weight: bold;
      color: #007aff;
    }
  }
}

.task-list {
  .task-item {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    .task-info {
      margin-bottom: 20rpx;
      .task-title {
        font-size: 32rpx;
        font-weight: bold;
        display: block;
        margin-bottom: 10rpx;
      }
      .task-progress {
        font-size: 28rpx;
        color: #666;
      }
    }
    .progress-bar {
      width: 100%;
      height: 20rpx;
      background-color: #eee;
      border-radius: 10rpx;
      overflow: hidden;
      .progress-fill {
        height: 100%;
        background-color: #007aff;
        transition: width 0.3s;
      }
    }
  }
  .no-tasks {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}
</style>