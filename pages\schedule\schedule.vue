<template>
  <view class="modern-page">
    <!-- 头部区域 -->
    <view class="modern-header">
      <text class="header-title">进度追踪</text>
      <text class="header-subtitle">可视化学习进度，掌握学习节奏</text>
      <view class="header-decoration"></view>
    </view>

    <!-- 内容区域 -->
    <view class="modern-content">
      <!-- 进度概览卡片 -->
      <view class="modern-card overview-card fade-in-up">
        <view class="card-header">
          <view class="header-icon">
            <text class="icon-text">📊</text>
          </view>
          <text class="card-title">学习概览</text>
        </view>

        <view class="overview-grid">
          <view class="overview-item">
            <text class="overview-value">{{ totalTasks }}</text>
            <text class="overview-label">总任务数</text>
          </view>
          <view class="overview-item">
            <text class="overview-value completed">{{ completedTasks }}</text>
            <text class="overview-label">已完成</text>
          </view>
          <view class="overview-item">
            <text class="overview-value rate">{{ completionRate }}%</text>
            <text class="overview-label">完成率</text>
          </view>
        </view>
      </view>

      <!-- 任务进度列表 -->
      <view class="tasks-section" v-if="tasks.length > 0">
        <view class="section-header">
          <text class="section-title">任务进度</text>
          <text class="section-subtitle">{{ tasks.length }}个任务</text>
        </view>

        <view class="task-list">
          <view class="modern-card task-item fade-in-up" v-for="(task, index) in tasks" :key="index">
            <view class="task-header">
              <view class="task-icon" :class="{ 'completed': task.progress === 100 }">
                <text class="icon-text">{{ task.progress === 100 ? '✅' : '📝' }}</text>
              </view>
              <view class="task-info">
                <text class="task-title">{{ task.title }}</text>
                <text class="task-progress">{{ task.progress }}% 完成</text>
              </view>
            </view>

            <view class="progress-container">
              <view class="progress-bar">
                <view class="progress-fill" :style="{ width: task.progress + '%' }"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="no-tasks" v-if="tasks.length === 0">
        <view class="empty-icon">
          <text class="icon-text">📊</text>
        </view>
        <text class="empty-text">暂无任务</text>
        <text class="empty-desc">完成计划和提醒后，这里将显示进度信息</text>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 任务列表
const tasks = ref([]);

import { apiRequest } from '@/utils/request.js';
// 加载任务数据（全部后端）
const loadTasks = async () => {
  try {
    // 获取计划
    const planRes = await apiRequest({ url: '/api/plans', method: 'GET' });
    const plans = (planRes || []).map(plan => ({
      title: plan.title,
      progress: plan.completed ? 100 : 0,
    }));
    // 获取提醒
    let reminders = [];
    try {
      const reminderRes = await apiRequest({ url: '/api/reminders', method: 'GET' });
      reminders = (reminderRes || []).map(reminder => {
        const reminderTime = new Date(reminder.time);
        const now = new Date();
        const progress = reminderTime < now ? 0 : 50;
        return { title: reminder.title, progress };
      });
    } catch {}
    tasks.value = [...plans, ...reminders];
  } catch (e) {
    tasks.value = [];
  }
};

// 保存任务数据（这里仅为演示，实际由其他组件保存）
const saveTasks = () => {
  // 不直接保存 tasks，因为它是从 plans 和 reminders 派生的
  // 如果需要独立存储任务，可以在这里实现
};

// 计算统计数据
const totalTasks = computed(() => tasks.value.length);
const completedTasks = computed(() => tasks.value.filter(task => task.progress === 100).length);
const completionRate = computed(() => {
  if (totalTasks.value === 0) return 0;
  const totalProgress = tasks.value.reduce((sum, task) => sum + task.progress, 0);
  return Math.round(totalProgress / totalTasks.value);
});

// 组件挂载时加载数据
onMounted(() => {
  loadTasks();
});
</script>

<style lang="scss">
// 使用全局现代化主题样式
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

// 概览卡片样式
.overview-card {
  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .header-icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: var(--plan-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16rpx;

      .icon-text {
        font-size: 24rpx;
        color: white;
      }
    }

    .card-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--gray-800);
    }
  }

  .overview-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24rpx;

    .overview-item {
      text-align: center;

      .overview-value {
        display: block;
        font-size: 36rpx;
        font-weight: 700;
        color: var(--plan-gradient);
        margin-bottom: 8rpx;

        &.completed {
          color: var(--checkin-gradient);
        }

        &.rate {
          color: var(--monitor-gradient);
        }
      }

      .overview-label {
        font-size: 22rpx;
        color: var(--gray-500);
        font-weight: 500;
      }
    }
  }
}

// 任务列表区域样式
.tasks-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .section-title {
      font-size: 28rpx;
      font-weight: 600;
      color: var(--white);
    }

    .section-subtitle {
      font-size: 22rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.task-list {
  .task-item {
    padding: 32rpx 24rpx;
    margin-bottom: 20rpx;

    .task-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;

      .task-icon {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20rpx;
        background: var(--gray-200);
        transition: all 0.3s ease;

        &.completed {
          background: var(--checkin-gradient);
        }

        .icon-text {
          font-size: 24rpx;
          color: var(--gray-600);
        }

        &.completed .icon-text {
          color: white;
        }
      }

      .task-info {
        flex: 1;

        .task-title {
          font-size: 28rpx;
          font-weight: 600;
          color: var(--gray-800);
          margin-bottom: 6rpx;
          display: block;
        }

        .task-progress {
          font-size: 22rpx;
          color: var(--gray-500);
          display: block;
        }
      }
    }

    .progress-container {
      .progress-bar {
        width: 100%;
        height: 12rpx;
        background: var(--gray-200);
        border-radius: 6rpx;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: var(--plan-gradient);
          border-radius: 6rpx;
          transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
    }
  }
}

.no-tasks {
  text-align: center;
  padding: 80rpx 20rpx;

  .empty-icon {
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto 24rpx;
    background: var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon-text {
      font-size: 60rpx;
      color: var(--gray-400);
    }
  }

  .empty-text {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: var(--gray-600);
    margin-bottom: 8rpx;
  }

  .empty-desc {
    display: block;
    font-size: 22rpx;
    color: var(--gray-400);
  }
}
</style>