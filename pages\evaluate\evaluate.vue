<template>
  <view class="evaluate">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">学习评价</text>
    </view>

    <!-- 总体评价 -->
    <view class="overall-evaluation">
      <view class="evaluation-header">
        <text class="evaluation-title">总体评价</text>
      </view>
      <view class="evaluation-content">
        <text class="score-label">综合得分</text>
        <text class="score-value">{{ overallScore }}</text>
        <text class="evaluation-text">{{ overallComment }}</text>
      </view>
    </view>

    <!-- 详细评价 -->
    <view class="detailed-evaluation">
      <view class="section-title">
        <text>详细评价</text>
      </view>
      <view class="evaluation-list">
        <view class="evaluation-item" v-for="(item, index) in evaluations" :key="index">
          <text class="item-label">{{ item.label }}</text>
          <text class="item-score">{{ item.label === '学习时长' ? `${item.score}分钟` : `${item.score}%` }}</text>
          <text class="item-comment">{{ item.comment }}</text>
        </view>
      </view>
    </view>

    <!-- 提交反馈 -->
    <view class="feedback-container">
      <view class="section-title">
        <text>提交反馈</text>
      </view>
      <textarea class="feedback-input" v-model="feedback" placeholder="请输入您的反馈意见..."></textarea>
      <button class="submit-button" @click="submitFeedback">提交</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 总体评价
const overallScore = ref(0);
const overallComment = ref('');

// 详细评价
const evaluations = ref([]);

import { apiRequest } from '@/utils/request.js';
const feedback = ref('');

// 加载数据
const loadData = () => {
  try {
    // 加载 Monitor.vue 数据
    const storedMonitorRecords = uni.getStorageSync('monitorRecords') || '[]';
    const monitorRecords = JSON.parse(storedMonitorRecords);
    const monitorData = calculateMonitorData(monitorRecords);

    // 加载 Dailypunch.vue 数据
    const storedPunchRecords = uni.getStorageSync('punchRecords') || '[]';
    const punchRecords = JSON.parse(storedPunchRecords);
    const punchData = calculatePunchData(punchRecords);

    // 设置详细评价
    evaluations.value = [
      { label: '专注度', score: monitorData.avgFocus, comment: generateFocusComment(monitorData.avgFocus) },
      { label: '完成率', score: punchData.completionRate, comment: generateCompletionComment(punchData.completionRate) },
      { label: '学习时长', score: monitorData.totalStudyTime, comment: generateStudyTimeComment(monitorData.totalStudyTime) },
    ];

    // 计算总体评价（专注度和完成率的平均值）
    overallScore.value = Math.round((monitorData.avgFocus + punchData.completionRate) / 2);
    overallComment.value = generateOverallComment(overallScore.value);

    // 加载反馈
    const storedFeedbacks = uni.getStorageSync('feedbacks') || '[]';
    feedbacks.value = JSON.parse(storedFeedbacks);
  } catch (e) {
    console.error('加载数据失败', e);
    evaluations.value = [
      { label: '专注度', score: 0, comment: '暂无数据' },
      { label: '完成率', score: 0, comment: '暂无数据' },
      { label: '学习时长', score: 0, comment: '暂无数据' },
    ];
    overallScore.value = 0;
    overallComment.value = '暂无评价数据';
    feedbacks.value = [];
  }
};

// 计算 Monitor.vue 数据
const calculateMonitorData = (records) => {
  const focusLevels = [];
  let totalStudyTime = 0;

  records.forEach(record => {
    const match = record.detail.match(/平均专注度 (\d+)%/);
    if (match) {
      focusLevels.push(parseInt(match[1]));
    }
  });

  // 计算总监测时长（分钟）
  totalStudyTime = records.reduce((sum, record) => {
    const timeMatch = record.detail.match(/监测时长: (\d{2}):(\d{2}):(\d{2})/);
    if (timeMatch) {
      const hours = parseInt(timeMatch[1]);
      const minutes = parseInt(timeMatch[2]);
      const seconds = parseInt(timeMatch[3]);
      return sum + (hours * 60) + minutes + (seconds / 60); // 转换为分钟
    }
    return sum;
  }, 0);

  const avgFocus = focusLevels.length > 0 ? Math.round(focusLevels.reduce((sum, val) => sum + val, 0) / focusLevels.length) : 0;

  return { avgFocus, totalStudyTime: Math.round(totalStudyTime) };
};

// 计算 Dailypunch.vue 数据
const calculatePunchData = (records) => {
  const totalPunchDays = records.filter(record => record.punched).length;
  const startDate = new Date('2025-01-01'); // 假设起始日期
  const today = new Date();
  const totalDays = Math.ceil((today - startDate) / (1000 * 60 * 60 * 24));
  const completionRate = totalDays > 0 ? Math.round((totalPunchDays / totalDays) * 100) : 0;

  return { completionRate };
};

// 生成评价评论
const generateFocusComment = (score) => {
  if (score >= 70) return '专注时间较长，效率高。';
  if (score >= 50) return '专注度一般，需提升稳定性。';
  return '专注度较低，建议优化学习环境。';
};

const generateCompletionComment = (score) => {
  if (score >= 70) return '大部分任务按时完成。';
  if (score >= 50) return '完成率中等，需加强坚持。';
  return '完成率较低，建议调整计划。';
};

const generateStudyTimeComment = (minutes) => {
  if (minutes >= 600) return '学习投入非常充分。'; // 10小时
  if (minutes >= 300) return '学习投入稳定。'; // 5小时
  return '学习时长较短，建议增加投入。';
};

const generateOverallComment = (score) => {
  if (score >= 70) return '您的学习表现优秀，继续保持！';
  if (score >= 50) return '学习表现良好，稍作调整更佳。';
  return '学习表现有待提升，建议优化学习策略。';
};

// 提交反馈
const submitFeedback = async () => {
  if (!feedback.value.trim()) {
    uni.showToast({ title: '反馈内容不能为空', icon: 'none' });
    return;
  }
  try {
    await apiRequest({ url: '/api/feedback', method: 'POST', data: { content: feedback.value } });
    uni.showToast({ title: '反馈提交成功', icon: 'success' });
    feedback.value = '';
  } catch (e) {
    uni.showToast({ title: e?.error || '提交失败', icon: 'none' });
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
.evaluate {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.overall-evaluation {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .evaluation-header {
    margin-bottom: 20rpx;
    .evaluation-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  .evaluation-content {
    text-align: center;
    .score-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .score-value {
      font-size: 48rpx;
      font-weight: bold;
      color: #007aff;
      display: block;
      margin-bottom: 20rpx;
    }
    .evaluation-text {
      font-size: 28rpx;
      color: #333;
      line-height: 40rpx;
    }
  }
}

.detailed-evaluation {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .section-title {
    margin-bottom: 20rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  .evaluation-list {
    .evaluation-item {
      padding: 20rpx 0;
      border-bottom: 2rpx solid #eee;
      &:last-child {
        border-bottom: none;
      }
      .item-label {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10rpx;
      }
      .item-score {
        font-size: 28rpx;
        color: #007aff;
        display: block;
        margin-bottom: 10rpx;
      }
      .item-comment {
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.feedback-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .section-title {
    margin-bottom: 20rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  .feedback-input {
    width: 100%;
    height: 200rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .submit-button {
    width: 100%;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
  }
}
</style>