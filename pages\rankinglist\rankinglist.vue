<template>
  <view class="ranking-list">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">连续打卡排行榜</text>
      <text class="info-icon" @click="showInfo">?</text>
    </view>

    <!-- 排行榜内容 -->
    <view class="ranking-content">
      <view class="ranking-item" v-for="(user, index) in rankingList" :key="index">
        <view class="ranking-rank" :class="{ 'top-three': index < 3 }">
          <text>{{ index + 1 }}</text>
        </view>
        <view class="ranking-avatar">
          <image :src="user.avatar" mode="aspectFill" class="avatar"></image>
        </view>
        <view class="ranking-info">
          <text class="ranking-name">{{ user.name }} {{ user.flag }}</text>
          <text class="ranking-score">{{ user.streakDays }} 天</text>
        </view>
      </view>
      <view class="no-ranking" v-if="rankingList.length === 0">
        <text>暂无排行数据</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 排行榜数据
import { apiRequest } from '@/utils/request.js';
const rankingList = ref([]);

// 加载排行榜数据（API）
const loadData = async () => {
  try {
    const res = await apiRequest({ url: '/api/rankings/streak', method: 'GET' });
    rankingList.value = res || [];
  } catch (e) {
    rankingList.value = [];
    uni.showToast({ title: '加载排行榜失败', icon: 'none' });
  }
};

// 计算 Dailypunch.vue 数据
const calculatePunchData = (records) => {
  const sortedRecords = records.sort((a, b) => new Date(b.date) - new Date(a.date));
  let streakDays = 0;
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  let currentDate = sortedRecords.some(r => r.date === formatDate(today) && r.punched)
    ? today
    : sortedRecords.some(r => r.date === formatDate(yesterday) && r.punched)
    ? yesterday
    : null;

  if (currentDate) {
    for (const record of sortedRecords) {
      const recordDate = new Date(record.date);
      if (record.punched && formatDate(recordDate) === formatDate(currentDate)) {
        streakDays++;
        currentDate.setDate(currentDate.getDate() - 1);
      } else if (recordDate < currentDate) {
        break;
      }
    }
  }

  return { streakDays };
};
// 格式化日期
const formatDate = (date) => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 显示用法说明
const showInfo = () => {
  uni.showModal({
    title: '排行榜',
    content: '排行榜显示你和好友的连续打卡天数。你的打卡数据来自每日打卡记录，排名按连续打卡天数降序排列。',
    showCancel: false,
    confirmText: '知道了',
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
.ranking-list {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
  .info-icon {
    font-size: 36rpx;
    color: #007aff;
    padding: 10rpx;
  }
}

.ranking-content {
  .ranking-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 20rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    .ranking-rank {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background-color: #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #fff;
      margin-right: 20rpx;
      &.top-three {
        background-color: #007aff;
        &:nth-child(1) {
          background-color: #ffd700; /* 第一名金色 */
        }
        &:nth-child(2) {
          background-color: #c0c0c0; /* 第二名银色 */
        }
        &:nth-child(3) {
          background-color: #cd7f32; /* 第三名铜色 */
        }
      }
    }
    .ranking-avatar {
      margin-right: 20rpx;
      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 40rpx;
        border: 2rpx solid #007aff;
      }
    }
    .ranking-info {
      flex: 1;
      .ranking-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        display: block;
        margin-bottom: 10rpx;
      }
      .ranking-score {
        font-size: 28rpx;
        color: #666;
      }
    }
  }
  .no-ranking {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}
</style>