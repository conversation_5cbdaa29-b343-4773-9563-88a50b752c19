/**
 * Gemini AI 图像识别服务
 * 用于分析学习状态图像并返回专注度和疲劳指数
 */

// Gemini API 配置
const GEMINI_API_KEY = 'AIzaSyBcTSbJVxQj4_atCBq3UuZxy88HfER1Rog'; // 内置API密钥
const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent';

/**
 * 将图像文件转换为base64格式
 * @param {String} imagePath - 图像文件路径
 * @returns {Promise<String>} base64编码的图像数据
 */
function imageToBase64(imagePath) {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath: imagePath,
      encoding: 'base64',
      success: (res) => {
        resolve(res.data);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

/**
 * 调用Gemini API分析图像
 * @param {String} base64Image - base64编码的图像数据
 * @returns {Promise<Object>} 分析结果 {focusLevel, fatigueLevel, confidence, description}
 */
async function analyzeImageWithGemini(base64Image) {
  const prompt = `请分析这张图片中人物的学习状态，从以下几个维度进行评估：

1. 专注度 (0-100分)：
   - 眼神是否专注于学习材料
   - 坐姿是否端正
   - 是否有分心行为（如玩手机、东张西望等）

2. 疲劳指数 (0-100分)：
   - 眼部疲劳程度（眼睛是否疲惫、眯眼等）
   - 面部表情是否显示疲劳
   - 身体姿态是否显示疲惫

请以JSON格式返回结果，包含以下字段：
{
  "focusLevel": 数字(0-100),
  "fatigueLevel": 数字(0-100),
  "confidence": 数字(0-100),
  "description": "简短的状态描述"
}

只返回JSON，不要其他文字。`;

  const requestBody = {
    contents: [{
      parts: [
        {
          text: prompt
        },
        {
          inline_data: {
            mime_type: "image/jpeg",
            data: base64Image
          }
        }
      ]
    }],
    generationConfig: {
      temperature: 0.1,
      topK: 32,
      topP: 1,
      maxOutputTokens: 1024,
    }
  };

  try {
    const response = await uni.request({
      url: `${GEMINI_API_URL}?key=${GEMINI_API_KEY}`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
      },
      data: requestBody,
      timeout: 30000
    });

    if (response.statusCode === 200 && response.data.candidates && response.data.candidates.length > 0) {
      const content = response.data.candidates[0].content.parts[0].text;

      // 尝试解析JSON响应
      try {
        const result = JSON.parse(content.trim());

        // 验证返回的数据格式
        if (typeof result.focusLevel === 'number' &&
            typeof result.fatigueLevel === 'number' &&
            typeof result.confidence === 'number') {

          // 确保数值在合理范围内
          result.focusLevel = Math.max(0, Math.min(100, result.focusLevel));
          result.fatigueLevel = Math.max(0, Math.min(100, result.fatigueLevel));
          result.confidence = Math.max(0, Math.min(100, result.confidence));

          return result;
        } else {
          throw new Error('Invalid response format');
        }
      } catch (parseError) {
        console.error('Failed to parse Gemini response:', parseError);
        throw new Error('AI响应格式错误');
      }
    } else {
      throw new Error('Gemini API请求失败');
    }
  } catch (error) {
    console.error('Gemini API Error:', error);
    throw error;
  }
}

/**
 * 分析学习状态图像的主要接口
 * @param {String} imagePath - 图像文件路径
 * @returns {Promise<Object>} 分析结果
 */
export async function analyzeStudyState(imagePath) {
  try {
    // 转换图像为base64
    const base64Image = await imageToBase64(imagePath);

    // 调用Gemini API分析
    const result = await analyzeImageWithGemini(base64Image);

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Study state analysis failed:', error);
    return {
      success: false,
      error: error.message || '图像分析失败',
      // 返回默认值作为降级方案
      data: {
        focusLevel: Math.round(Math.random() * 100),
        fatigueLevel: Math.round(Math.random() * 100),
        confidence: 0,
        description: '图像识别失败，使用随机数据'
      }
    };
  }
}

/**
 * 直接使用base64数据分析学习状态（用于H5环境）
 * @param {String} base64Image - base64编码的图像数据
 * @returns {Promise<Object>} 分析结果
 */
export async function analyzeStudyStateFromBase64(base64Image) {
  try {
    // 直接调用Gemini API分析
    const result = await analyzeImageWithGemini(base64Image);

    return {
      success: true,
      data: result
    };
  } catch (error) {
    console.error('Study state analysis failed:', error);
    return {
      success: false,
      error: error.message || '图像分析失败',
      // 返回默认值作为降级方案
      data: {
        focusLevel: Math.round(Math.random() * 100),
        fatigueLevel: Math.round(Math.random() * 100),
        confidence: 0,
        description: '图像识别失败，使用随机数据'
      }
    };
  }
}

/**
 * 检查Gemini服务是否可用
 * @returns {Boolean} 服务是否可用
 */
export function isGeminiServiceAvailable() {
  return true; // API密钥已内置，服务始终可用
}


