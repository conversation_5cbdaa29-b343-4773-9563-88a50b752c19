<template>
  <view class="setting-page">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">设置</text>
    </view>

    <!-- 设置项列表 -->
    <view class="setting-list">
      <!-- 通知开关 -->
      <view class="setting-item">
        <text class="setting-label">通知提醒</text>
        <switch class="setting-switch" :checked="notificationEnabled" @change="toggleNotification" />
      </view>

      <!-- 检查更新 -->
      <view class="setting-item" @click="checkUpdate">
        <text class="setting-label">检查更新</text>
        <text class="setting-value">当前版本：1.0.3</text>
      </view>

      <!-- 新增的评价与反馈 -->
      <view class="setting-item" @click="goToFeedback">
        <text class="setting-label">评价与反馈</text>
        <text class="setting-value">给我们提出建议</text>
      </view>

      <!-- 重置数据 -->
      <view class="setting-item danger" @click="resetData">
        <text class="setting-label danger-text">重置数据</text>
        <text class="setting-tip">将清除所有数据</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 通知开关状态
const notificationEnabled = ref(true);

// 加载通知开关状态
const loadNotificationSetting = () => {
  try {
    const storedSetting = uni.getStorageSync('notificationEnabled');
    if (storedSetting !== '') {
      notificationEnabled.value = storedSetting === 'true';
    } else {
      notificationEnabled.value = true;
      saveNotificationSetting();
    }
  } catch (e) {
    console.error('加载通知设置失败', e);
    notificationEnabled.value = true;
    saveNotificationSetting();
  }
};

// 保存通知开关状态
const saveNotificationSetting = () => {
  try {
    uni.setStorageSync('notificationEnabled', notificationEnabled.value.toString());
  } catch (e) {
    console.error('保存通知设置失败', e);
  }
};

// 切换通知开关
const toggleNotification = (e) => {
  notificationEnabled.value = e.detail.value;
  saveNotificationSetting();
  uni.showToast({ title: `通知已${notificationEnabled.value ? '开启' : '关闭'}`, icon: 'success' });
};

// 检查更新
const checkUpdate = () => {
  uni.showLoading({ title: '检查更新中...' });
  setTimeout(() => {
    uni.hideLoading();
    uni.showModal({
      title: '检查更新',
      content: '当前已是最新版本：1.0.3',
      showCancel: false,
      confirmText: '确定',
    });
  }, 1000);
};

// 新增导航到反馈页面
const goToFeedback = () => {
  uni.navigateTo({
    url: '/pages/helpfeedback/helpfeedback'
  });
};

// 重置数据
const resetData = () => {
  uni.showModal({
    title: '重置数据',
    content: '此操作将清除所有数据（包括用户信息、好友列表、动态等），是否继续？',
    confirmText: '确定',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        uni.clearStorageSync();
        uni.showToast({ title: '数据已重置', icon: 'success' });
        uni.reLaunch({
          url: '/pages/enter/enter',
        });
      }
    },
  });
};

// 组件挂载时加载通知设置
onMounted(() => {
  loadNotificationSetting();
});
</script>

<style lang="scss">
.setting-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.setting-list {
  margin-bottom: 30rpx;
  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    &.danger {
      border: 2rpx solid #ff3b30;
    }
    .setting-label {
      font-size: 32rpx;
      color: #333;
      &.danger-text {
        color: #ff3b30;
      }
    }
    .setting-value {
      font-size: 28rpx;
      color: #666;
    }
    .setting-tip {
      font-size: 24rpx;
      color: #ff3b30;
    }
    .setting-switch {
      transform: scale(0.8);
    }
  }
}
</style>