<template>
  <view class="time-reminder">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">时间提醒</text>
    </view>

    <!-- 提醒列表 -->
    <view class="reminder-list">
      <view class="reminder-item" v-for="(reminder, index) in reminders" :key="index">
        <view class="reminder-info">
          <text class="reminder-title">{{ reminder.title }}</text>
          <text class="reminder-time">提醒时间: {{ reminder.time }}</text>
        </view>
        <view class="reminder-actions">
          <button class="action-button" @click="editReminder(index)">编辑</button>
          <button class="action-button delete-button" @click="deleteReminder(index)">删除</button>
        </view>
      </view>
      <view class="no-reminders" v-if="reminders.length === 0">
        <text>暂无提醒，快去添加吧！</text>
      </view>
    </view>

    <!-- 添加提醒按钮 -->
    <view class="add-reminder-container">
      <button class="add-button" @click="showAddModal = true">
        <text class="button-icon">➕</text>
        <text class="button-text">添加新提醒</text>
      </button>
    </view>

    <!-- 添加/编辑提醒模态框 -->
    <view class="modal" v-if="showAddModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">{{ isEditing ? '编辑提醒' : '添加提醒' }}</text>
          <text class="close-button" @click="closeModal">×</text>
        </view>
        <view class="modal-body">
          <view class="form-group">
            <text class="form-label">提醒内容</text>
            <input type="text" v-model="newReminder.title" class="form-input" placeholder="请输入提醒内容" />
          </view>
          <view class="form-group">
            <text class="form-label">提醒时间</text>
            <picker mode="date" :value="newReminder.date" @change="onDateChange">
              <view class="form-input">{{ newReminder.date || '选择日期' }}</view>
            </picker>
            <picker mode="time" :value="newReminder.time" @change="onTimeChange">
              <view class="form-input">{{ newReminder.time || '选择时间' }}</view>
            </picker>
          </view>
        </view>
        <view class="modal-footer">
          <button class="submit-button" @click="saveReminder">{{ isEditing ? '保存' : '添加' }}</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

const showAddModal = ref(false);
const isEditing = ref(false);
const editingIndex = ref(-1);

import { apiRequest } from '@/utils/request.js';
const reminders = ref([]);
const newReminder = reactive({
  title: '',
  date: '',
  time: '',
  repeat: '',
});

// 加载提醒数据（后端API）
const loadReminders = async () => {
  try {
    const res = await apiRequest({ url: '/api/reminders', method: 'GET' });
    reminders.value = res || [];
  } catch (e) {
    reminders.value = [];
  }
};

// 设置提醒通知
const scheduleNotification = (reminder) => {
  const [date, time] = reminder.time.split(' ');
  const [year, month, day] = date.split('-').map(Number);
  const [hour, minute] = time.split(':').map(Number);

  const reminderTime = new Date(year, month - 1, day, hour, minute).getTime();
  const now = Date.now();

  if (reminderTime > now) {
    const delay = reminderTime - now;
    setTimeout(() => {
      uni.showToast({
        title: `提醒: ${reminder.title}`,
        icon: 'none',
        duration: 5000,
      });
    }, delay);
  }
};

// 日期选择
const onDateChange = (e) => {
  newReminder.date = e.detail.value;
};

// 时间选择
const onTimeChange = (e) => {
  newReminder.time = e.detail.value;
};

// 添加或保存提醒
const saveReminder = () => {
  if (!newReminder.title || !newReminder.date || !newReminder.time) {
    uni.showToast({ title: '请填写完整信息', icon: 'none' });
    return;
  }
  const fullTime = `${newReminder.date} ${newReminder.time}`;
  if (isEditing.value) {
    reminders.value[editingIndex.value] = { title: newReminder.title, time: fullTime };
  } else {
    reminders.value.push({ title: newReminder.title, time: fullTime });
  }
  scheduleNotification({ title: newReminder.title, time: fullTime }); // 设置通知
  saveReminders();
  closeModal();
};

// 编辑提醒
const editReminder = (index) => {
  isEditing.value = true;
  editingIndex.value = index;
  const [date, time] = reminders.value[index].time.split(' ');
  newReminder.title = reminders.value[index].title;
  newReminder.date = date;
  newReminder.time = time;
  showAddModal.value = true;
};

// 删除提醒
const deleteReminder = (index) => {
  reminders.value.splice(index, 1);
  saveReminders();
};

// 关闭模态框并重置
const closeModal = () => {
  showAddModal.value = false;
  isEditing.value = false;
  editingIndex.value = -1;
  newReminder.title = '';
  newReminder.date = '';
  newReminder.time = '';
};

// 组件挂载时加载数据并检查提醒
const checkReminders = () => {
  loadReminders();
  reminders.value.forEach((reminder) => {
    scheduleNotification(reminder);
  });
};

onMounted(() => {
  checkReminders();
});
</script>

<style lang="scss">
.time-reminder {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.reminder-list {
  margin-bottom: 30rpx;
  .reminder-item {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .reminder-info {
      .reminder-title {
        font-size: 32rpx;
        font-weight: bold;
        display: block;
        margin-bottom: 10rpx;
      }
      .reminder-time {
        font-size: 28rpx;
        color: #666;
      }
    }

    .reminder-actions {
      display: flex;
      gap: 20rpx;
      .action-button {
        height: 60rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        border-radius: 10rpx;
        background-color: #007aff;
        color: #fff;
        border: none;
        &.delete-button {
          background-color: #ff3b30;
        }
      }
    }
  }

  .no-reminders {
    text-align: center;
    font-size: 32rpx;
    color: #666;
    padding: 40rpx;
  }
}

.add-reminder-container {
  .add-button {
    width: 100%;
    height: 100rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    .button-icon {
      margin-right: 20rpx;
      font-size: 40rpx;
    }
  }
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #eee;
  .modal-title {
    font-size: 36rpx;
    font-weight: bold;
  }
  .close-button {
    font-size: 48rpx;
    color: #999;
  }
}

.modal-body {
  padding: 30rpx;
}

.form-group {
  margin-bottom: 30rpx;
  .form-label {
    display: block;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
  }
  .form-input {
    width: 100%;
    height: 80rpx;
    border: 2rpx solid #ddd;
    border-radius: 10rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    line-height: 80rpx;
    color: #333;
  }
}

.modal-footer {
  padding: 30rpx;
  border-top: 2rpx solid #eee;
  .submit-button {
    width: 100%;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
  }
}
</style>