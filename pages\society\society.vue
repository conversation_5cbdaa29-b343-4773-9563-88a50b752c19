<template>
  <view class="society-page">
    <!-- 功能按钮区域 -->
    <view class="button-section">
      <view class="section-title">
        <text>社交功能</text>
      </view>

      <!-- 排行榜按钮 -->
      <view class="button-container">
        <navigator url="/pages/rankinglist/rankinglist">
          <button class="custom-button">
            <text class="button-icon">📋</text>
            <text class="button-text">排行榜</text>
          </button>
        </navigator>
      </view>

      <!-- 好友按钮 -->
      <view class="button-container">
        <navigator url="/pages/friends/friends">
          <button class="custom-button">
            <text class="button-icon">👥</text>
            <text class="button-text">好友</text>
          </button>
        </navigator>
      </view>

      <!-- 社交按钮 -->
      <view class="button-container">
        <navigator url="/pages/social/social">
          <button class="custom-button">
            <text class="button-icon">💬</text>
            <text class="button-text">社交</text>
          </button>
        </navigator>
      </view>
    </view>
  </view>
</template>

<script setup>
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
.society-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 功能按钮区域 */
.button-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    margin-bottom: 30rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

/* 按钮容器样式 */
.button-container {
  margin-bottom: 30rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

/* 自定义按钮样式 */
.custom-button {
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  color: #333;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  transition: background-color 0.3s, color 0.3s;
  
  .button-icon {
    margin-right: 20rpx;
    font-size: 40rpx;
  }
  
  .button-text {
    flex: 1;
    text-align: left;
  }
  
  &:active {
    background-color: #f0f8ff;
  }
}
</style>