<template>
  <view class="index-page">
    <!-- 轮播图区域 -->
    <view class="banner-container">
      <swiper circular="true" indicator-dots indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff" autoplay>
        <swiper-item>
          <image src="../../common/images/1.png" mode="aspectFill" class="banner-image"></image>
        </swiper-item>
        <swiper-item>
          <image src="../../common/images/2.png" mode="aspectFill" class="banner-image"></image>
        </swiper-item>
        <swiper-item>
          <image src="../../common/images/3.png" mode="aspectFill" class="banner-image"></image>
        </swiper-item>
      </swiper>
    </view>

    <!-- 功能按钮区域 -->
    <view class="button-section">
      <view class="section-title">
        <text>功能导航</text>
      </view>

      <!-- 计划按钮 -->
      <view class="button-container">
        <navigator url="/pages/plan/plan">
          <button class="custom-button">
            <text class="button-icon">📅</text>
            <text class="button-text">计划</text>
          </button>
        </navigator>
      </view>

      <!-- 每日打卡按钮 -->
      <view class="button-container">
        <navigator url="/pages/daily/daily">
          <button class="custom-button">
            <text class="button-icon">✅</text>
            <text class="button-text">每日打卡</text>
          </button>
        </navigator>
      </view>

      <!-- 学习监测按钮 -->
      <view class="button-container">
        <navigator url="/pages/monitor/monitor">
          <button class="custom-button">
            <text class="button-icon">📈</text>
            <text class="button-text">学习监测</text>
          </button>
        </navigator>
      </view>

      <!-- 成就按钮 -->
      <view class="button-container">
        <navigator url="/pages/achievement/achievement">
          <button class="custom-button">
            <text class="button-icon">🏆</text>
            <text class="button-text">成就</text>
          </button>
        </navigator>
      </view>
    </view>
  </view>
</template>

<script setup>
// 可以在这里添加逻辑代码
</script>

<style lang="scss">
.index-page {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图区域 */
.banner-container {
  width: 100%;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  swiper {
    width: 100%;
    height: 340rpx;
    
    .banner-image {
      width: 100%;
      height: 100%;
    }
  }
}

/* 功能按钮区域 */
.button-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  .section-title {
    margin-bottom: 30rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

/* 按钮容器样式 */
.button-container {
  margin-bottom: 30rpx;
  &:last-child {
    margin-bottom: 0;
  }
}

/* 自定义按钮样式 */
.custom-button {
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  border: 2rpx solid #007aff;
  border-radius: 20rpx;
  color: #333;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  transition: background-color 0.3s, color 0.3s;
  
  .button-icon {
    margin-right: 20rpx;
    font-size: 40rpx;
  }
  
  .button-text {
    flex: 1;
    text-align: left;
  }
  
  &:active {
    background-color: #f0f8ff;
  }
}
</style>