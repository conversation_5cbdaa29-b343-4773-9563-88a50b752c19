<template>
  <view class="index-page">
    <!-- 头部欢迎区域 -->
    <view class="header-section">
      <view class="welcome-text">
        <text class="greeting">{{ greeting }}</text>
        <text class="subtitle">开始您的高效学习之旅</text>
      </view>
      <view class="header-decoration"></view>
    </view>

    <!-- 轮播图区域 -->
    <view class="banner-container">
      <swiper
        circular="true"
        indicator-dots="true"
        indicator-color="rgba(255,255,255,0.3)"
        indicator-active-color="#6366f1"
        autoplay="true"
        class="modern-swiper"
      >
        <swiper-item>
          <view class="banner-item">
            <image src="../../common/images/1.png" mode="aspectFill" class="banner-image"></image>
            <view class="banner-overlay">
              <text class="banner-title">智能学习助手</text>
              <text class="banner-desc">AI驱动的个性化学习体验</text>
            </view>
          </view>
        </swiper-item>
        <swiper-item>
          <view class="banner-item">
            <image src="../../common/images/2.png" mode="aspectFill" class="banner-image"></image>
            <view class="banner-overlay">
              <text class="banner-title">高效时间管理</text>
              <text class="banner-desc">科学规划，提升学习效率</text>
            </view>
          </view>
        </swiper-item>
        <swiper-item>
          <view class="banner-item">
            <image src="../../common/images/3.png" mode="aspectFill" class="banner-image"></image>
            <view class="banner-overlay">
              <text class="banner-title">成就激励系统</text>
              <text class="banner-desc">记录每一次进步与成长</text>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 功能网格区域 -->
    <view class="features-section">
      <view class="section-header">
        <text class="section-title">核心功能</text>
        <text class="section-subtitle">探索强大的学习工具</text>
      </view>

      <view class="features-grid">
        <!-- 计划功能卡片 -->
        <navigator url="/pages/plan/plan" class="feature-card-nav">
          <view class="feature-card plan-card">
            <view class="card-icon">
              <text class="icon-text">📋</text>
            </view>
            <view class="card-content">
              <text class="card-title">智能计划</text>
              <text class="card-desc">AI辅助制定学习计划</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 每日打卡功能卡片 -->
        <navigator url="/pages/daily/daily" class="feature-card-nav">
          <view class="feature-card checkin-card">
            <view class="card-icon">
              <text class="icon-text">✓</text>
            </view>
            <view class="card-content">
              <text class="card-title">每日打卡</text>
              <text class="card-desc">养成良好学习习惯</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 学习监测功能卡片 -->
        <navigator url="/pages/monitor/monitor" class="feature-card-nav">
          <view class="feature-card monitor-card">
            <view class="card-icon">
              <text class="icon-text">📊</text>
            </view>
            <view class="card-content">
              <text class="card-title">学习监测</text>
              <text class="card-desc">实时专注度分析</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>

        <!-- 成就功能卡片 -->
        <navigator url="/pages/achievement/achievement" class="feature-card-nav">
          <view class="feature-card achievement-card">
            <view class="card-icon">
              <text class="icon-text">🏆</text>
            </view>
            <view class="card-content">
              <text class="card-title">成就系统</text>
              <text class="card-desc">记录学习里程碑</text>
            </view>
            <view class="card-arrow">
              <text class="arrow-icon">→</text>
            </view>
          </view>
        </navigator>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration"></view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// 获取当前时间的问候语
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const greeting = ref(getGreeting())

onMounted(() => {
  greeting.value = getGreeting()
})
</script>

<style lang="scss">

.index-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }
}

/* 头部欢迎区域 */
.header-section {
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  z-index: 1;

  .welcome-text {
    .greeting {
      display: block;
      font-size: 48rpx;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 12rpx;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .subtitle {
      display: block;
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 400;
    }
  }

  .header-decoration {
    position: absolute;
    top: 40rpx;
    right: 40rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
  }
}

/* 轮播图区域 */
.banner-container {
  margin: 0 30rpx 40rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow:
    0 20rpx 40rpx rgba(0, 0, 0, 0.1),
    0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1;

  .modern-swiper {
    width: 100%;
    height: 380rpx;
  }

  .banner-item {
    position: relative;
    width: 100%;
    height: 100%;

    .banner-image {
      width: 100%;
      height: 100%;
    }

    .banner-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      padding: 60rpx 40rpx 40rpx;

      .banner-title {
        display: block;
        font-size: 36rpx;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 8rpx;
      }

      .banner-desc {
        display: block;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
      }
    }
  }
}

/* 功能区域 */
.features-section {
  padding: 0 30rpx 40rpx;
  position: relative;
  z-index: 1;

  .section-header {
    margin-bottom: 40rpx;

    .section-title {
      display: block;
      font-size: 40rpx;
      font-weight: 700;
      color: #ffffff;
      margin-bottom: 8rpx;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    }

    .section-subtitle {
      display: block;
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
      font-weight: 400;
    }
  }
}

/* 功能网格 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.feature-card-nav {
  text-decoration: none;
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 32rpx 24rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
  }

  &:active {
    transform: translateY(4rpx);
    box-shadow:
      0 8rpx 20rpx rgba(0, 0, 0, 0.1),
      0 4rpx 8rpx rgba(0, 0, 0, 0.06);
  }

  .card-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20rpx;

    .icon-text {
      font-size: 40rpx;
      color: #ffffff;
      font-weight: normal;
    }
  }

  .card-content {
    margin-bottom: 16rpx;

    .card-title {
      display: block;
      font-size: 28rpx;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 8rpx;
    }

    .card-desc {
      display: block;
      font-size: 22rpx;
      color: #6b7280;
      line-height: 1.4;
    }
  }

  .card-arrow {
    position: absolute;
    top: 24rpx;
    right: 24rpx;

    .arrow-icon {
      font-size: 24rpx;
      color: #9ca3af;
      font-weight: 600;
    }
  }
}

/* 不同功能卡片的主题色 */
.plan-card .card-icon {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.checkin-card .card-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.monitor-card .card-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.achievement-card .card-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

/* 底部装饰 */
.bottom-decoration {
  height: 100rpx;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.1), transparent);
  position: relative;
  z-index: 1;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }

  .feature-card {
    padding: 28rpx 20rpx;
  }
}
</style>