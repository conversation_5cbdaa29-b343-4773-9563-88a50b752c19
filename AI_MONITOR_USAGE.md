# AI学习监测功能使用指南

## 🎯 功能简介

AI学习监测功能使用Google Gemini AI技术，通过分析用户的学习状态照片来评估专注度和疲劳指数，提供智能的学习建议。

## 🚀 快速开始

### 1. 启动监测
- 打开学习监测页面
- 点击"开始监测"按钮
- 系统会启动摄像头预览

### 2. AI分析
- 确保AI识别开关处于开启状态
- 点击"拍照分析"按钮
- 等待AI分析完成（通常几秒钟）

### 3. 查看结果
- 专注度：0-100分，分数越高表示越专注
- 疲劳指数：0-100分，分数越高表示越疲劳
- 识别置信度：AI对分析结果的信心程度
- 状态描述：AI对当前学习状态的文字描述

### 4. 停止监测
- 点击"停止监测"按钮
- 系统会生成基于整个监测期间的学习建议
- 建议会自动保存到学习行为记录中

## 💡 使用技巧

### 获得更准确的分析结果
1. **光线充足**：确保面部有足够的光线照明
2. **面部清晰**：保持面部在摄像头视野内且清晰可见
3. **避免遮挡**：不要用手或其他物品遮挡面部
4. **自然状态**：保持自然的学习状态，不要刻意摆拍

### 最佳使用时机
- 学习开始时拍照建立基准
- 感觉状态变化时及时拍照
- 每30-60分钟定期拍照监测
- 学习结束前拍照评估效果

## 🔧 功能说明

### AI识别模式
- **开启状态**：使用Gemini AI分析学习状态
- **分析内容**：面部表情、眼神专注度、坐姿等
- **结果输出**：专注度、疲劳指数、置信度、描述

### 随机模式
- **关闭AI**：切换到随机数据生成模式
- **自动更新**：每分钟自动生成新的随机数据
- **降级方案**：当AI分析失败时自动启用

### 数据记录
- **实时显示**：当前专注度和疲劳指数
- **历史追踪**：记录每分钟的数据变化
- **平均计算**：基于整个监测期间的平均值
- **智能建议**：根据平均分数生成个性化建议

## ⚠️ 注意事项

### 隐私保护
- 拍摄的照片仅用于实时AI分析
- 照片不会保存在设备上
- 照片不会上传到任何服务器
- 所有分析都在本地完成

### 网络要求
- 需要稳定的网络连接
- 需要能够访问Google服务
- 建议使用WiFi以获得更好的体验

### 设备权限
- 需要摄像头权限
- 需要网络访问权限
- 首次使用时会提示授权

## 🔍 故障排除

### 摄像头问题
- **无法启动**：检查摄像头权限是否授予
- **画面黑屏**：重启应用或检查摄像头硬件
- **画面模糊**：清洁摄像头镜头

### AI分析问题
- **分析失败**：检查网络连接
- **结果异常**：确保光线充足，面部清晰
- **响应缓慢**：检查网络速度

### 数据问题
- **数据不更新**：重新拍照分析
- **记录丢失**：检查应用存储权限
- **建议不准确**：增加拍照频率提高数据准确性

## 📊 评分标准

### 专注度评分
- **90-100分**：高度专注，学习状态极佳
- **70-89分**：较为专注，学习状态良好
- **50-69分**：一般专注，可以继续学习
- **30-49分**：注意力分散，建议短暂休息
- **0-29分**：严重分心，建议立即休息

### 疲劳指数评分
- **0-29分**：精神饱满，状态极佳
- **30-49分**：轻微疲劳，状态良好
- **50-69分**：中度疲劳，建议适当休息
- **70-89分**：较为疲劳，建议休息调整
- **90-100分**：严重疲劳，必须休息

## 🎯 学习建议解读

系统会根据平均专注度和疲劳指数生成个性化建议：

- **状态良好**：继续保持，合理安排学习时间
- **状态一般**：适当调整学习节奏或休息5分钟
- **状态不佳**：建议休息10分钟或切换轻松任务
- **状态较差**：建议休息15分钟并调整学习内容

## 📱 界面说明

- **摄像头预览**：显示当前摄像头画面
- **拍照分析按钮**：点击进行AI状态分析
- **AI识别开关**：切换AI模式和随机模式
- **状态显示区**：显示当前专注度和疲劳指数
- **置信度信息**：显示AI分析的可信程度
- **学习建议**：显示基于分析结果的建议
- **行为记录**：查看历史学习记录

通过合理使用AI学习监测功能，您可以更好地了解自己的学习状态，及时调整学习策略，提高学习效率！
