<template>
  <view class="saying">
    <!-- 标题 -->
    <view class="header">
      <text class="header-title">激励语录</text>
    </view>

    <!-- 当前语录 -->
    <view class="current-saying">
      <text class="saying-text">"{{ currentSaying.text }}"</text>
      <text class="saying-author">—— {{ currentSaying.author }}</text>
    </view>

    <!-- 操作按钮 -->
    <view class="action-container">
      <button class="action-button" @click="nextSaying">下一条</button>
      <button class="action-button favorite-button" @click="toggleFavorite">
        {{ isFavorite ? '取消收藏' : '收藏' }}
      </button>
    </view>

    <!-- 收藏列表 -->
    <view class="favorite-list">
      <view class="section-title">
        <text>我的收藏</text>
      </view>
      <view class="favorite-item" v-for="(item, index) in favorites" :key="index">
        <text class="favorite-text">"{{ item.text }}"</text>
        <text class="favorite-author">—— {{ item.author }}</text>
        <button class="remove-button" @click="removeFavorite(index)">移除</button>
      </view>
      <view class="no-favorites" v-if="favorites.length === 0">
        <text>暂无收藏语录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 语录数据（20条）
const sayings = ref([
  { text: '不积跬步，无以至千里', author: '荀子' },
  { text: '成功的秘诀在于坚持', author: '爱迪生' },
  { text: '每一次努力，都是在靠近梦想', author: '佚名' },
  { text: '只要路是对的，就不怕路远', author: '佚名' },
  { text: '今天的努力，是为了明天的自由', author: '佚名' },
  { text: '失败是成功之母', author: '谚语' },
  { text: '行动是成功的阶梯', author: '佚名' },
  { text: '相信自己，你比想象中更强大', author: '佚名' },
  { text: '时间是最好的老师', author: '佚名' },
  { text: '不怕慢，只怕站', author: '谚语' },
  { text: '努力不一定成功，但放弃一定失败', author: '佚名' },
  { text: '生活不会辜负每一个努力的人', author: '佚名' },
  { text: '坚持到底，就是胜利', author: '佚名' },
  { text: '每一步都在塑造更好的自己', author: '佚名' },
  { text: '成功属于那些不轻言放弃的人', author: '佚名' },
  { text: '用汗水浇灌梦想', author: '佚名' },
  { text: '没有天生的强者，只有不懈的努力', author: '佚名' },
  { text: '勇敢追梦，未来可期', author: '佚名' },
  { text: '学习是改变命运的钥匙', author: '佚名' },
  { text: '每一天都是新的开始', author: '佚名' },
]);

// 当前语录
const currentSaying = ref({ text: '', author: '' });
const isFavorite = ref(false);

// 收藏列表
const favorites = ref([]);

// 加载收藏数据
const loadData = () => {
  try {
    const storedFavorites = uni.getStorageSync('favorites') || '[]';
    favorites.value = JSON.parse(storedFavorites);
    // 随机选择一条语录作为初始显示
    setRandomSaying();
  } catch (e) {
    console.error('加载收藏数据失败', e);
    favorites.value = [];
    setRandomSaying();
  }
};

// 保存收藏数据
const saveData = () => {
  try {
    uni.setStorageSync('favorites', JSON.stringify(favorites.value));
  } catch (e) {
    console.error('保存收藏数据失败', e);
  }
};

// 随机选择一条语录
const setRandomSaying = () => {
  const randomIndex = Math.floor(Math.random() * sayings.value.length);
  currentSaying.value = sayings.value[randomIndex];
  isFavorite.value = favorites.value.some(f => f.text === currentSaying.value.text);
};

// 下一条语录（随机）
const nextSaying = () => {
  setRandomSaying();
};

// 切换收藏状态
const toggleFavorite = () => {
  if (isFavorite.value) {
    const index = favorites.value.findIndex(f => f.text === currentSaying.value.text);
    if (index !== -1) favorites.value.splice(index, 1);
  } else {
    favorites.value.push({ ...currentSaying.value });
  }
  isFavorite.value = !isFavorite.value;
  saveData();
};

// 移除收藏
const removeFavorite = (index) => {
  favorites.value.splice(index, 1);
  isFavorite.value = favorites.value.some(f => f.text === currentSaying.value.text);
  saveData();
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss">
.saying {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 30rpx;
  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.current-saying {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .saying-text {
    font-size: 32rpx;
    color: #333;
    display: block;
    margin-bottom: 20rpx;
    line-height: 48rpx;
  }
  .saying-author {
    font-size: 28rpx;
    color: #666;
  }
}

.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  .action-button {
    width: 45%;
    height: 80rpx;
    background-color: #007aff;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
    &.favorite-button {
      background-color: #28a745;
    }
  }
}

.favorite-list {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  .section-title {
    margin-bottom: 20rpx;
    text {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  .favorite-item {
    padding: 20rpx 0;
    border-bottom: 2rpx solid #eee;
    position: relative;
    &:last-child {
      border-bottom: none;
    }
    .favorite-text {
      font-size: 28rpx;
      color: #333;
      display: block;
      margin-bottom: 10rpx;
    }
    .favorite-author {
      font-size: 26rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }
    .remove-button {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      height: 60rpx;
      padding: 0 20rpx;
      background-color: #ff3b30;
      color: #fff;
      border: none;
      border-radius: 10rpx;
      font-size: 26rpx;
    }
  }
  .no-favorites {
    text-align: center;
    font-size: 28rpx;
    color: #666;
    padding: 40rpx;
  }
}
</style>