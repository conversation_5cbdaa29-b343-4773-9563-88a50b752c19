{"version": 3, "file": "timereminder.js", "sources": ["pages/timereminder/timereminder.vue", "../../../../../../HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGltZXJlbWluZGVyL3RpbWVyZW1pbmRlci52dWU"], "sourcesContent": ["<template>\n\t<view>\n\t\t\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t\n\t\t}\n\t}\n</script>\n\n<style>\n\n</style>\n", "import MiniProgramPage from 'D:/.Resources/大创/自律助手vue/自律助手/自律助手/自律助手/pages/timereminder/timereminder.vue'\nwx.createPage(MiniProgramPage)"], "names": [], "mappings": ";;AAOC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,SAAS,CAET;AACD;;;;;ACfD,GAAG,WAAW,eAAe;"}